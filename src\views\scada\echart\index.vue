<template>
  <div class="page">
    <div class="warning-view">
      <div class="label">预警信息</div>
      <div
        class="scroll-view"
        ref="scrollViewRef"
        @mouseenter="onMouseenter"
        @mouseleave="onMouseleave"
      >
        <div ref="listRef" class="list" v-for="(p, n) in 2" :key="n">
          <div class="item" v-for="(item, index) in data" :key="index">
            <div class="content">预警消息 {{ index }}</div>
            <div class="time">2024-11-06</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onBeforeMount, onMounted, onBeforeUnmount, nextTick } from "vue";
const data = ref(); //列表数据
const listRef = ref(); //列表dom
const scrollViewRef = ref(); //滚动区域dom


let intervalId = null;
let isAutoScrolling = true; //是否自动滚动标识

//获取列表数据
const getData = () => {
  //模拟接口请求列表数据
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      //生成10条数据
      let list = new Array(10).fill().map((item, index) => index);
      resolve(list);
    }, 1000);
  });
};

onMounted(async () => {
  data.value = await getData();
  nextTick(() => {
    autoScrolling();
  });
});

//设置自动滚动
const autoScrolling = () => {
  intervalId = setInterval(() => {
    if (scrollViewRef.value.scrollTop < listRef.value[0].clientHeight) {
      scrollViewRef.value.scrollTop += isAutoScrolling ? 1 : 0;
    } else {
      scrollViewRef.value.scrollTop = 0;
    }
  }, 20);
};

onBeforeUnmount(() => {
  //离开页面清理定时器
  intervalId && clearInterval(intervalId);
});

//鼠标进入，停止滚动
const onMouseenter = () => {
  isAutoScrolling = false;
};
//鼠标移出，继续滚动
const onMouseleave = () => {
  isAutoScrolling = true;
};
</script>

<style scoped>
.page {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #010c1e;
  color: #fff;
}
.warning-view {
  width: 400px;
  height: 400px;
  border: 1px solid #fff;
  display: flex;
  flex-direction: column;
}
.label {
  color: #fff;
  padding: 20px;
  font-size: 22px;
}
.scroll-view {
  flex: 1;
  height: 0;
  width: 100%;
  overflow-y: auto;
}
.list {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}
.item {
  width: 100%;
  height: 50px;
  min-height: 50px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #eee;
}
/**
*隐藏滚动条
 */
 ::-webkit-scrollbar{
  display: none;
 }
</style>

