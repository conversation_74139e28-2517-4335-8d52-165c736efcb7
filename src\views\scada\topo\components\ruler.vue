<template>
  <div>1111</div>
  <!-- <div v-show="rulerToggle" :style="{ width: windowWidth + 'px', height: windowHeight + 'px', position: position }"
    class="ScaleBox" onselectstart="return false;">
    <div id="levelRuler" class="ScaleRuler_h" @mousedown.stop="levelDragRuler">
      <span v-for="(item, index) in xScale" :key="index" :style="{ left: index * 50 + 2 + 'px' }" class="n">{{ item.id
        }}</span>
    </div>
    <div id="verticalRuler" class="ScaleRuler_v" @mousedown.stop="verticalDragRuler">
      <span v-for="(item, index) in yScale" :key="index" :style="{ top: index * 50 + 2 + 'px' }" class="n">{{ item.id
        }}</span>
    </div>
    <div id="levelDottedLine" :style="{ top: verticalDottedTop + 'px' }" class="RefDot_h" />
    <div id="verticalDottedLine" :style="{ left: levelDottedLeft + 'px' }" class="RefDot_v" />
    <div v-for="item in levelLineList" :id="item.id" :title="item.title" :style="{ top: item.top + 'px' }"
      :key="item.id" class="RefLine_h" @mousedown="dragLevelLine(item.id)" />
    <div v-for="item in verticalLineList" :id="item.id" :title="item.title" :style="{ left: item.left + 'px' }"
      :key="item.id" class="RefLine_v" @mousedown="dragVerticalLine(item.id)" />
    <div id="content" :style="{ left: contentLayout.left + 'px', top: contentLayout.top + 'px' }" style="padding: 18px">
      <slot />
    </div>
  </div> -->
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';

interface Line {
  id: string;
  title: string;
  top?: number;
  left?: number;
}

interface Scale {
  id: number;
}

const position = ref<any>('relative');
const isHotKey = ref<boolean>(true);
const isScaleRevise = ref<boolean>(false);
const presetLine = ref<Array<{ type: string, site: number }>>([]);
const contentLayout = ref<{ top: number; left: number }>({ top: 0, left: 0 });
const parent = ref<boolean>(false);

const windowWidth = ref<number>(0);
const windowHeight = ref<number>(0);
const xScale = ref<Scale[]>([]);
const yScale = ref<Scale[]>([]);
const topSpacing = ref<number>(0);
const leftSpacing = ref<number>(0);
const isDrag = ref<boolean>(false);
const dragFlag = ref<string>('');
const levelLineList = ref<Line[]>([]);
const verticalLineList = ref<Line[]>([]);
const levelDottedLeft = ref<number>(-999);
const verticalDottedTop = ref<number>(-999);
const rulerWidth = ref<number>(0);
const rulerHeight = ref<number>(0);
const dragLineId = ref<string>('');
const keyCode = ref<{ r: number }>({ r: 82 });
const rulerToggle = ref<boolean>(true);

const init = () => {
  xScale.value = [];
  yScale.value = [];
  box();
  scaleCalc();
};

const box = () => {
  if (isScaleRevise.value) {
    // 根据内容部分进行刻度修正
    const content = document.getElementById('content');
    const contentLeft = content?.offsetLeft || 0;
    const contentTop = content?.offsetTop || 0;
    for (let i = 0; i < contentLeft; i += 1) {
      if (i % 50 === 0 && i + 50 <= contentLeft) {
        xScale.value.push({ id: i });
      }
    }
    for (let i = 0; i < contentTop; i += 1) {
      if (i % 50 === 0 && i + 50 <= contentTop) {
        yScale.value.push({ id: i });
      }
    }
  }
  setTimeout(() => {
    if (parent.value) {
      const style = window.getComputedStyle(document.body, null);
      windowWidth.value = parseInt(style.getPropertyValue('width'), 10);
      windowHeight.value = parseInt(style.getPropertyValue('height'), 10);
    } else {
      windowWidth.value = document.documentElement.clientWidth - leftSpacing.value;
      windowHeight.value = document.documentElement.clientHeight - topSpacing.value;
    }
  }, 500);
  const verticalRulerElement = document.getElementById('verticalRuler');
  const levelRulerElement = document.getElementById('levelRuler');
  rulerWidth.value = verticalRulerElement?.clientWidth || 0;
  rulerHeight.value = levelRulerElement?.clientHeight || 0;
  topSpacing.value = levelRulerElement?.getBoundingClientRect().y || 0;
  leftSpacing.value = verticalRulerElement?.getBoundingClientRect().x || 0;
};
// 获取窗口宽与高
const scaleCalc = () => {
  for (let i = 0; i < windowWidth.value; i += 1) {
    if (i % 50 === 0) {
      xScale.value.push({ id: i });
    }
  }
  for (let i = 0; i < windowHeight.value; i += 1) {
    if (i % 50 === 0) {
      yScale.value.push({ id: i });
    }
  }
};
// 计算刻度
const newLevelLine = () => {
  isDrag.value = true;
  dragFlag.value = 'x';
};
// 生成一个水平参考线
const newVerticalLine = () => {
  isDrag.value = true;
  dragFlag.value = 'y';
};
// 生成一个垂直参考线
const dottedLineMove = (event: MouseEvent) => {
  switch (dragFlag.value) {
    case 'x':
      if (isDrag.value) {
        verticalDottedTop.value = event.pageY - topSpacing.value;
      }
      break;
    case 'y':
      if (isDrag.value) {
        levelDottedLeft.value = event.pageX - leftSpacing.value;
      }
      break;
    case 'l':
      if (isDrag.value) {
        verticalDottedTop.value = event.pageY - topSpacing.value;
      }
      break;
    case 'v':
      if (isDrag.value) {
        levelDottedLeft.value = event.pageX - leftSpacing.value;
      }
      break;
    default:
      break;
  }
};
// 虚线移动
const dottedLineUp = (event: MouseEvent) => {
  if (isDrag.value) {
    isDrag.value = false;
    switch (dragFlag.value) {
      case 'x':
        levelLineList.value.push({
          id: 'levelLine' + (levelLineList.value.length + 1),
          title: event.pageY + 1 - topSpacing.value - 18 + 'px',
          top: event.pageY - topSpacing.value + 1,
        });
        break;
      case 'y':
        verticalLineList.value.push({
          id: 'verticalLine' + (verticalLineList.value.length + 1),
          title: event.pageX + 1 - leftSpacing.value - 18 + 'px',
          left: event.pageX - leftSpacing.value + 1,
        });
        break;
      case 'l':
        if (event.pageY - topSpacing.value < rulerHeight.value) {
          let Index, id;
          levelLineList.value.forEach((item, index) => {
            if (item.id === dragLineId.value) {
              Index = index;
              id = item.id;
            }
          });
          if (Index !== undefined) {
            levelLineList.value.splice(Index, 1, {
              id: id!,
              title: -600 + 'px',
              top: -600,
            });
          }
        } else {
          let Index, id;
          levelLineList.value.forEach((item, index) => {
            if (item.id === dragLineId.value) {
              Index = index;
              id = item.id;
            }
          });
          if (Index !== undefined) {
            levelLineList.value.splice(Index, 1, {
              id: id!,
              title: event.pageY + 1 - topSpacing.value - 18 + 'px',
              top: event.pageY - topSpacing.value + 1,
            });
          }
        }
        break;
      case 'v':
        verticalLineList.value.push({
          id: 'verticalLine' + (verticalLineList.value.length + 1),
          title: event.pageX + 'px',
          left: event.pageX,
        });
        break;
      default:
        break;
    }
  }
};
// 虚线松开
const levelDragRuler = () => {
  newLevelLine();
}
// 水平标尺处按下鼠标
const verticalDragRuler = () => {
  newVerticalLine();
}
// 垂直标尺处按下鼠标
const dragLevelLine = (id) => {
  isDrag.value = true;
  dragFlag.value = 'l';
  dragLineId.value = id;
}
// 水平线处按下鼠标
const dragVerticalLine = (id) => {
  isDrag.value = true;
  dragFlag.value = 'v';
  dragLineId.value = id;
}
// 垂直线处按下鼠标
const keyboard = (event: any) => {
  if (this.isHotKey) {
    switch (event.keyCode) {
      case this.keyCode.r:
        this.rulerToggle = !this.rulerToggle;
        break;
    }
  }
}
// 键盘事件
const quickGeneration = (params: any) => {
  if (params !== []) {
    params.forEach((item) => {
      switch (item.type) {
        case 'l':
          this.levelLineList.push({
            id: 'levelLine' + this.levelLineList.length + 1,
            title: item.site + 'px',
            top: item.site,
          });
          break;
        case 'v':
          this.verticalLineList.push({
            id: 'verticalLine' + this.verticalLineList.length + 1,
            title: item.site + 'px',
            left: item.site,
          });
          break;
        default:
          break;
      }
    });
  }
} // 快速生成参考线
onMounted(() => {
  document.documentElement.addEventListener('mousemove', dottedLineMove, true);
  document.documentElement.addEventListener('mouseup', dottedLineUp, true);
  document.documentElement.addEventListener('keyup', (event: KeyboardEvent) => {
    if (event.keyCode === keyCode.value.r) {
      // handle hotkey logic
    }
  }, true);
  init();
  // Add presetLine logic here if needed
  window.onresize = () => {
    init();
  };
});

onBeforeUnmount(() => {
  document.documentElement.removeEventListener('mousemove', dottedLineMove, true);
  document.documentElement.removeEventListener('mouseup', dottedLineUp, true);
  document.documentElement.removeEventListener('keyup', () => { }, true);
});
</script>
