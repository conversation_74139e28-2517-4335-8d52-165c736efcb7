<template>
    <!-- 组态主界面 -->
    <div class="topo-main-wrap">
        <div class="content-wrap" ref="rulerToolBox">
            <vue-ruler-tool :parent="true" :is-scale-revise="true" ref="rulerTool" :is-hot-key="false">
                <div tabindex="0" id="surface-edit-layer" class="topo-layer"
                    :class="{ 'topo-layer-view-selected': selectedIsLayer }" :style="layerStyle"
                    @click="onLayerClick($event)" @mouseup="onLayerMouseup($event)"
                    @mousemove="onLayerMousemove($event)" @mousedown="onLayerMousedown($event)"
                    @keyup.delete="removeItem()" @dragover.prevent @drop="onDrop" @contextmenu.prevent="onContextmenu"
                    @keydown.ctrl.67.stop="copyItem" @keydown.ctrl.86.stop="pasteItem"
                    @keydown.ctrl.90.stop="counterStore.undo" @keydown.ctrl.89.stop="counterStore.redo">
                    <template v-for="(component, index) in configData.components" :key="index">
                        <div tabindex="0" class="topo-layer-view"
                            :class="{ 'topo-layer-view-selected': selectedComponentMap[component.identifier] == undefined ? false : true }"
                            v-show="component.style.visible == undefined ? true : component.style.visible"
                            @click.stop="clickComponent(index, component, $event)"
                            @mousedown.stop="controlMousedown(component, $event, index)" @keyup.delete="removeItem()"
                            @keydown.up.exact.prevent="moveItems('up')"
                            @keydown.right.exact.prevent="moveItems('right')"
                            @keydown.down.exact.prevent="moveItems('down')"
                            @keydown.left.exact.prevent="moveItems('left')" @keydown.ctrl.67.stop="copyItem"
                            @keydown.ctrl.86.stop="pasteItem" @keydown.ctrl.90.stop="counterStore.undo"
                            @keydown.ctrl.89.stop="counterStore.redo" :style="{
                                left: component.style.position.x + 'px',
                                top: component.style.position.y + 'px',
                                width: component.style.position.w + 'px',
                                height: component.style.position.h + 'px',
                                backgroundColor: component.type == 'flow-bar' || component.type == 'flow-bar-dynamic' ? 'transparent' : component.style.backColor,
                                zIndex: component.style.zIndex,
                                transform: component.style.transformType,
                                opacity: component.style.opacity,
                                'border-radius': component.style.borderRadius + 'px',
                                'box-shadow': '0 0 ' + component.style.boxShadowWidth + 'px 0 ' + component.style.boxShadowColor,
                            }">
                            <component v-bind:is="parseView(component)" :detail="component" :editMode="true"
                                :selected="selectedComponentMap[component.identifier] ? true : false"
                                :ref="(el: any) => compRefs.value[index] = el" />
                            <div class="resize-left-top"
                                @mousedown.stop="resizeMousedown(component, $event, index, 'resize-lt')"
                                v-show="selectedComponentMap[component.identifier]"></div>
                            <div class="resize-left-center"
                                @mousedown.stop="resizeMousedown(component, $event, index, 'resize-lc')"
                                v-show="selectedComponentMap[component.identifier]"></div>
                            <div class="resize-left-bottom"
                                @mousedown.stop="resizeMousedown(component, $event, index, 'resize-lb')"
                                v-show="selectedComponentMap[component.identifier]"></div>
                            <div class="resize-right-top"
                                @mousedown.stop="resizeMousedown(component, $event, index, 'resize-rt')"
                                v-show="selectedComponentMap[component.identifier]"></div>
                            <div class="resize-right-center"
                                @mousedown.stop="resizeMousedown(component, $event, index, 'resize-rc')"
                                v-show="selectedComponentMap[component.identifier]"></div>
                            <div class="resize-right-bottom"
                                @mousedown.stop="resizeMousedown(component, $event, index, 'resize-rb')"
                                v-show="selectedComponentMap[component.identifier]"></div>
                            <div class="resize-center-top"
                                @mousedown.stop="resizeMousedown(component, $event, index, 'resize-ct')"
                                v-show="selectedComponentMap[component.identifier]"></div>
                            <div class="resize-center-bottom"
                                @mousedown.stop="resizeMousedown(component, $event, index, 'resize-cb')"
                                v-show="selectedComponentMap[component.identifier]"></div>
                        </div>
                    </template>

                    <div class="topo-frame-selection"
                        :style="{ width: frameSelectionDiv.width + 'px', height: frameSelectionDiv.height + 'px', top: frameSelectionDiv.top + 'px', left: frameSelectionDiv.left + 'px' }">
                    </div>
                </div>
            </vue-ruler-tool>
        </div>
    </div>
</template>
<script setup lang="ts">
import { computed, nextTick, onBeforeUnmount, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import VueRulerTool from './ruler.vue';
import FileSaver from 'file-saver';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import { listCenter, updateCenter } from '/@/api/scada/center';
import { getByGuid, saveDetailData } from '/@/api/scada/topo';
import eventBus from '/@/utils/eventBus';
import html2canvas from 'html2canvas';
import { useCounterStore } from '/@/stores/counterStore';
import { useAppStore } from '/@/stores/app'
import { checkByRectCollisionDetection } from '/@/utils/topo/index';
import topoUtil from '/@/utils/topo/topo-util';
import { deepCopy } from '/@/utils';
// 创建 store 实例
const counterStore = useCounterStore();
const appStore = useAppStore()
const route = useRoute();
// 定义子组件向父组件传值/事件
const emit = defineEmits(['recoveryFlagClick', 'revokeFlagClick', 'lockStatus']);
const baseApi = ref(import.meta.env.VITE_APP_BASE_API)
const moveItem = reactive({
    startX: 0,
    startY: 0,
})// 移动组件 相关变量
// resize组件 相关变量
const resizeItem = reactive({
    startPx: 0,
    startPy: 0,
    x: 0,
    y: 0,
    w: 0,
    h: 0,
})
const loadingInstance = ref<any | null>(null);
const rulerTool = ref()
const deviceZtRow = ref({
    scadaData: [] as any
})
let ztList = reactive([])// 所有组态列表
const id = ref() // 组态id
const guid = ref() // 组态标识
const zoom = ref(100) // 缩放大小
const configDataHis = ref([]) // 操作中的组态数据temp
const selectFlag = ref(0) // 选中组件标识
const importLoading = ref(false) //加载
const isMultiple = ref(false)//撤销恢复标志位
const operateFlag = ref(0) //撤销恢复标志位
const selectComponent = ref(null) // 选中组件
const flag = ref('') // 当前操作标志位
const curControl = ref() // 当前操作组件
const curIndex = ref(-1)
interface MyComponentInstance {
  onResize: () => void;
}
const compRefs = ref<(MyComponentInstance | null)[]>([]); // 定义为 MyComponentInstance 或 null 的数组
const isFullFlag = ref(null)
const selectIndx = ref(null)
const newestConfigData = ref(null)// 操作中的组态数据temp
const uploadImport = reactive({
    open: false, // 是否显示弹出层（用户导入）
    title: '', // 弹出层标题（用户导入）
    isUploading: false, // 是否禁用上传
    updateSupport: 0, // 是否更新已经存在的用户数据
    headers: { Authorization: 'Bearer ' + sessionStorage.getItem('Admin-Token-WebTopo') }, // 设置上传的请求头部
    url: '', // 上传的地址
})
const uploadImportRef = ref()
const frameSelectionDiv = reactive({
    width: 0,
    height: 0,
    top: 10,
    left: 10,
    startX: 0,
    startY: 0,
    startPageX: 0,
    startPageY: 0,
})
//初始化卡尺
const initRuler = () => {
    nextTick(() => {
        rulerTool.value.init();
    });
}
//调整视图大小
const handleZoom = (value: any) => {
    zoom.value = value;
}
//导入组态json
const handleImport = () => {
    uploadImport.title = '组态导入';
    uploadImport.open = true;
    uploadImport.url = baseApi.value + '/scada/center/importJson?guid=' + guid.value;
}

// 文件上传中处理
const handleFileUploadProgress = () => {
    uploadImport.isUploading = true;
}
// 文件上传成功处理
const handleFileSuccess = (response: any, file: any, fileList: any) => {
    uploadImport.open = false;
    uploadImport.isUploading = false;
    uploadImportRef.value.clearFiles();
    ElMessageBox.alert(response.msg, '导入结果', {
        dangerouslyUseHTMLString: true,
    });
    importLoading.value = false;
    getZtDetails();
}
// 提交上传文件
const submitFileForm = () => {
    uploadImportRef.value.submit();
    importLoading.value = true;
}
//导出组态json
const handleDownLoad = () => {
    const data = JSON.stringify(deviceZtRow.value);
    const blob = new Blob([data], { type: '' });
    FileSaver.saveAs(blob, document.title + '.json');
}
//撤销
const revoke = () => {
    // let levelLineList=this.$refs.rulerTool.levelLineList;
    // let verticalLineList=this.$refs.rulerTool.verticalLineList;
    if (selectFlag.value == configDataHis.value.length) {
        newestConfigData.value = deepCopy(configData);
    }
    selectFlag.value = selectFlag.value - 1;
    configDataHis.value.forEach((element: any, index: any) => {
        if (selectFlag.value == index) {
            loadDefaultTopoData(element);
        }
    });
}
//恢复
const recovery = () => {
    selectFlag.value = selectFlag.value + 1;
    if (selectFlag.value >= configDataHis.value.length) {
        loadDefaultTopoData(newestConfigData.value);
    } else {
        configDataHis.value.forEach((element: any, index: any) => {
            if (selectFlag.value == index) {
                loadDefaultTopoData(element);
            }
        });
    }
    // console.log(selectFlag.value,this.configData);
}
const onTabContextmenu = (event: any) => {
    this.$contextmenu({
        items: [
            {
                label: '全屏',
                icon: 'el-icon-full-screen',
                onClick: () => {
                    this.clickFullscreen();
                },
            },
            {
                label: '重新加载',
                divided: true,
                icon: 'el-icon-refresh',
                onClick: () => {
                    this.$router.go(0);
                },
            },
        ],
        event, // 鼠标事件信息
        customClass: 'custom-class', // 自定义菜单 class
        zIndex: 9999, // 菜单样式 z-index
        minWidth: 230, // 主菜单最小宽度
    });

    return true;
}
const onContextmenu = (event: any) => {
    if (selectedComponentMap && selectedComponentMap[selectedComponents].type == 'VR') {
        return;
    }
    let isDisabled = false;
    if (selectedComponents.length > 0) {
        isDisabled = false;
    } else {
        isDisabled = true;
    }

    // console.log(isDisabled);
    let that = this;
    this.$contextmenu({
        items: [
            {
                label: '取消',
                icon: 'el-icon-circle-close',
                onClick: () => { },
            },
            {
                label: '复制',
                icon: 'el-icon-document-copy',
                disabled: isDisabled,
                onClick: () => {
                    copyItem();
                    pasteItem();
                },
            },
            {
                label: '删除',
                icon: 'el-icon-delete',
                disabled: isDisabled,
                onClick: () => {
                    that.removeItem();
                },
            },
            {
                label: '置顶',
                icon: 'el-icon-top',
                disabled: isDisabled,
                onClick: () => {
                    that.$emit('menuClick', '置顶');
                },
            },
            {
                label: '置底',
                icon: 'el-icon-bottom',
                disabled: isDisabled,
                onClick: () => {
                    that.$emit('menuClick', '置底');
                },
            },
            {
                label: '旋转',
                minWidth: 0,
                disabled: isDisabled,
                children: [
                    {
                        label: '顺时针90°',
                        icon: 'el-icon-plus',
                        onClick: () => {
                            // console.log("顺时针90°");
                            that.$emit('menuClick', '顺时针90°');
                        },
                    },
                    {
                        label: '逆时针90°',
                        icon: 'el-icon-minus',
                        onClick: () => {
                            // console.log("逆时针90°");
                            that.$emit('menuClick', '逆时针90°');
                        },
                    },
                    {
                        label: '水平镜像',
                        icon: 'el-icon-d-arrow-left',
                        onClick: () => {
                            // console.log("水平镜像");
                            that.$emit('menuClick', '水平镜像');
                        },
                    },
                    {
                        label: '垂直镜像',
                        icon: 'el-icon-d-arrow-right',
                        onClick: () => {
                            // console.log("垂直镜像");
                            that.$emit('menuClick', '垂直镜像');
                        },
                    },
                    {
                        label: '自定义角度',
                        icon: 'el-icon-edit',
                        onClick: () => {
                            // console.log("自定义角度");
                            that.$emit('menuClick', '自定义角度');
                        },
                    },
                ],
            },
            {
                label: '对齐',
                minWidth: 0,
                disabled: isDisabled,
                children: [
                    {
                        label: '左对齐',
                        icon: 'el-icon-arrow-left',
                        onClick: () => {
                            that.alignClick('左对齐');
                        },
                    },
                    {
                        label: '右对齐',
                        icon: 'el-icon-arrow-right',
                        onClick: () => {
                            that.alignClick('右对齐');
                        },
                    },
                    {
                        label: '上对齐',
                        icon: 'el-icon-arrow-up',
                        onClick: () => {
                            that.alignClick('上对齐');
                        },
                    },
                    {
                        label: '下对齐',
                        icon: 'el-icon-arrow-down',
                        onClick: () => {
                            that.alignClick('下对齐');
                        },
                    },
                    {
                        label: '水平等间距',
                        icon: 'el-icon-sort',
                        onClick: () => {
                            that.alignClick('水平等间距');
                        },
                    },
                    {
                        label: '垂直等间距',
                        icon: 'el-icon-sort',
                        onClick: () => {
                            that.alignClick('垂直等间距');
                        },
                    },
                ],
            },
            {
                label: '组合',
                minWidth: 0,
                disabled: isDisabled,
                children: [
                    {
                        label: '组合',
                        icon: 'el-icon-connection',
                        onClick: () => {
                            that.makeUpClick('组合');
                        },
                    },
                    {
                        label: '取消组合',
                        icon: 'el-icon-link',
                        onClick: () => {
                            that.makeUpClick('取消组合');
                        },
                    },
                ],
            },
            {
                label: '锁定',
                minWidth: 0,
                disabled: isDisabled,
                children: [
                    {
                        label: '锁定',
                        icon: 'el-icon-lock',
                        onClick: () => {
                            that.handleLock('锁定');
                        },
                    },
                    {
                        label: '解锁',
                        icon: 'el-icon-unlock',
                        onClick: () => {
                            that.handleLock('解锁');
                        },
                    },
                ],
            },
            {
                label: '图库',
                divided: true,
                icon: 'el-icon-picture-outline',
                onClick: () => {
                    that.$emit('menuClick', '图库');
                },
            },
            {
                label: '预览',
                divided: true,
                icon: 'el-icon-view',
                onClick: () => {
                    that.$emit('menuClick', '预览');
                },
            },
            {
                label: '保存',
                divided: true,
                icon: 'el-icon-star-off',
                onClick: () => {
                    that.$emit('menuClick', '保存');
                },
            },
            {
                label: '重新加载',
                divided: true,
                icon: 'el-icon-refresh',
                onClick: () => {
                    this.$router.go(0);
                },
            },
        ],
        event, // 鼠标事件信息
        customClass: 'custom-class', // 自定义菜单 class
        zIndex: 9999, // 菜单样式 z-index
        minWidth: 230, // 主菜单最小宽度
    });

    return true;
}
const controlMousedown = (component: any, event: any, index: any) => {
    if (event.ctrlKey) {
        return;
    }
    flag.value = 'move';
    curControl.value = component;
    clickItem(component, index);
    moveItem.startX = event.pageX;
    moveItem.startY = event.pageY;
    //记录初始信息--move
    for (var key in selectedComponentMap) {
        var component = selectedComponentMap[key];
        component.style.temp = {};
        component.style.temp.position = {};
        component.style.temp.position.x = component.style.position.x;
        component.style.temp.position.y = component.style.position.y;
    }
}
const resizeMousedown = (component: any, event: any, index: any, flag: any) => {
    //resize-鼠标按下事件
    flag.value = flag;
    curControl.value = component;
    curIndex.value = index;
    clickItem(component, index);
    var dom = event.currentTarget;
    resizeItem.startPx = event.clientX;
    resizeItem.startPy = event.clientY;
    //记录初始信息-resize
    resizeItem.x = curControl.value.style.position.x;
    resizeItem.y = curControl.value.style.position.y;
    resizeItem.w = curControl.value.style.position.w;
    resizeItem.h = curControl.value.style.position.h;
}
const onLayerMousemove = (event: any) => {
    if (event.which != 1) {
        flag.value = '';
        return;
    }
    if (flag.value == '') return;
    if (flag.value.startsWith('resize')) {
        var dx = event.clientX - resizeItem.startPx,
            dy = event.clientY - resizeItem.startPy;
        switch (flag.value) {
            case 'resize-lt':
                curControl.value.style.position.x = resizeItem.x + dx;
                curControl.value.style.position.y = resizeItem.y + dy;
                dx = -dx;
                dy = -dy;
                break;
            case 'resize-lc':
                curControl.value.style.position.x = resizeItem.x + dx;
                dy = 0;
                dx = -dx;
                break;
            case 'resize-lb':
                curControl.value.style.position.x = resizeItem.x + dx;
                dx = -dx;
                break;
            case 'resize-rt':
                curControl.value.style.position.y = resizeItem.y + dy;
                dy = -dy;
                break;
            case 'resize-rc':
                dy = 0;
                break;
            case 'resize-rb':
                break;
            case 'resize-ct':
                curControl.value.style.position.y = resizeItem.y + dy;
                dx = 0;
                dy = -dy;
                break;
            case 'resize-cb':
                dx = 0;
                break;
        }
        if (resizeItem.w + dx > 20) {
            curControl.value.style.position.w = resizeItem.w + dx;
        }
        if (resizeItem.h + dy > 20) {
            curControl.value.style.position.h = resizeItem.h + dy;
        }
        //canvas组件需要重新渲染
        // DOM 还没有更新
        nextTick(() => {
            // DOM 更新了
            // var a = this.$refs['comp' + curIndex.value][0];
            // a.onResize();
            const a = compRefs.value[curIndex.value]; // 获取当前索引对应的组件引用
            if (a) {
                a.onResize(); // 调用 onResize 方法
            }
        });
    } else if (flag.value == 'move') {
        //移动组件
        var dx = event.pageX - moveItem.startX,
            dy = event.pageY - moveItem.startY;
        for (var key in selectedComponentMap.value) {
            var component = selectedComponentMap.value[key];
            if (!component.isLock) {
                component.style.position.x = component.style.temp.position.x + dx;
                component.style.position.y = component.style.temp.position.y + dy;
            }
        }
    } else if (flag.value == 'frame_selection') {
        onFrameSelection(event);
    }
}
const onLayerMousedown = (event: any) => {
    flag.value = 'frame_selection';
    frameSelectionDiv.startX = event.offsetX;
    frameSelectionDiv.startY = event.offsetY;
    frameSelectionDiv.startPageX = event.pageX;
    frameSelectionDiv.startPageY = event.pageY;
    isMultiple.value = true;
}
const onLayerClick = ($event: MouseEvent) => {
    if (isMultiple.value == false) {
        clearSelected();
        selectComponent.value = null;
        setLayerSelected(true);
    } else {
        isMultiple.value = false;
    }
}
const onFrameSelection = (event: any) => {
    var dx = event.pageX - frameSelectionDiv.startPageX;
    var dy = event.pageY - frameSelectionDiv.startPageY;
    frameSelectionDiv.width = Math.abs(dx);
    frameSelectionDiv.height = Math.abs(dy);
    if (dx > 0 && dy > 0) {
        frameSelectionDiv.top = frameSelectionDiv.startY;
        frameSelectionDiv.left = frameSelectionDiv.startX;
    } else if (dx > 0 && dy < 0) {
        frameSelectionDiv.top = frameSelectionDiv.startY + dy;
        frameSelectionDiv.left = frameSelectionDiv.startX;
    } else if (dx < 0 && dy > 0) {
        frameSelectionDiv.top = frameSelectionDiv.startY;
        frameSelectionDiv.left = frameSelectionDiv.startX + dx;
    } else if (dx < 0 && dy < 0) {
        frameSelectionDiv.top = frameSelectionDiv.startY + dy;
        frameSelectionDiv.left = frameSelectionDiv.startX + dx;
    }
    //判断各个组件是否在方框内
    var _this = this;
    var rect = {
        x: frameSelectionDiv.left,
        y: frameSelectionDiv.top,
        width: frameSelectionDiv.width,
        height: frameSelectionDiv.height,
    };
    var components = configData.value.components;
    components.forEach((component: any) => {
        var itemRect = {
            x: component.style.position.x,
            y: component.style.position.y,
            width: component.style.position.w,
            height: component.style.position.h,
        };
        if (checkByRectCollisionDetection(rect, itemRect)) {
            counterStore.addSelectedComponent(component);
        } else {
            counterStore.removeSelectedComponent(component);
        }
    });
    if (selectedComponents.length > 0) {
        setLayerSelected(false);
    } else {
        setLayerSelected(true);
    }
}
const onDrop = (event: any) => {
    event.preventDefault();
    var infoJson = event.dataTransfer.getData('my-info');
    var component = JSON.parse(infoJson);
    if (component.type == 'weather') {
        var components = configData.components;
        let isExist = false;
        components.forEach((componentL: any) => {
            if (component.type == 'weather') {
                isExist = true;
            }
        });
        if (isExist) {
            ElMessage.warning('天气组件已存在，不能重复添加')
            // this.$message({
            //     message: '天气组件已存在，不能重复添加',
            //     type: 'warning',
            // });
            return;
        }
    }
    if (checkAddComponent(component) == false) {
        return;
    }
    //判断当前着陆点是不是layer
    if (event.target.id == 'surface-edit-layer') {
        component.style.position.x = event.offsetX;
        component.style.position.y = event.offsetY;
    } else {
        //解决着陆灯不是layer的情形
        var layer = event.currentTarget;
        var position = layer.getBoundingClientRect();
        var x = event.clientX - position.left;
        var y = event.clientY - position.top;
        component.style.position.x = x;
        component.style.position.y = y;
    }
    //处理默认值
    counterStore.execute({
        op: 'add',
        component: component,
    });

    //默认选中，并点击
    clickItem(component, configData.components.length - 1);
}
const moveItems = (direction: any) => {
    var dx = 0,
        dy = 0;
    if (direction == 'up') {
        dy = -5;
    } else if (direction == 'right') {
        dx = 5;
    } else if (direction == 'down') {
        dy = 5;
    } else if (direction == 'left') {
        dx = -5;
    }
    counterStore.execute({
        op: 'move',
        dx: dx,
        dy: dy,
        items: selectedComponentMap.value,
    });
}
const checkAddComponent = (info: any) => {
    if (info == null) {
        this.$q.notify({
            type: 'negative',
            position: 'bottom-right',
            message: 'This component not surpport.',
        });
        return false;
    }
    return true;
}
const parseView = (component: any) => {
    return topoUtil.parseViewName(component);
}
const onLayerMouseup = (event: any) => {
    // console.log("组件回滚");
    if (flag.value.startsWith('resize')) {
        // var a = this.$refs['comp' + curIndex.value][0];
        // a.onResize();
        const a = compRefs.value[curIndex.value][0]; // 获取当前索引对应的组件引用
        if (a) {
            a.onResize(); // 调用 onResize 方法
        }
    } else if (flag.value == 'frame_selection') {
        //由于和onLayerClick冲突，这里模拟下点击空白区域
        onFrameSelection(event);
        frameSelectionDiv.width = 0;
        frameSelectionDiv.height = 0;
        frameSelectionDiv.top = 0;
        frameSelectionDiv.left = 0;
    } else if (flag.value == 'move') {
        //鼠标move只是方便用户预览，真正执行应该用命令，所以要先恢复
        var dx = event.pageX - moveItem.startX;
        var dy = event.pageY - moveItem.startY;
        for (var key in selectedComponentMap.value) {
            var component = selectedComponentMap.value[key];
            component.style.position.x = component.style.position.x - dx;
            component.style.position.y = component.style.position.y - dy;
        }
        counterStore.execute({
            op: 'move',
            dx: dx,
            dy: dy,
            items: selectedComponentMap.value,
        });
    }
    flag.value = '';
}
const clickItem = (component: any, index: any) => {
    operateFlag.value = operateFlag.value + 1;
    selectFlag.value = operateFlag.value;
    console.log('单击组件:', deepCopy(component));
    sessionStorage.setItem('operateFlag' + operateFlag.value, JSON.stringify(deepCopy(component)));
    selectComponent.value = component;
    selectIndx.value = index;
    setLayerSelected(false);
    if (selectedComponentMap.value[component.identifier] == undefined) {
        counterStore.setSelectedComponent(component);
    } else {
        //如果已经选中，则不做任何处理
        counterStore.setSelectedComponent(component);
    }
    if (component.identifiers) {
        component.identifiers.forEach((element: any) => {
            configData.components.forEach((ele: any) => {
                if (ele.identifier == element) {
                    Vue.set(selectedComponentMap.value, element, ele);
                }
            });
        });
    }
    emit('lockStatus', component.isLock);
}

const copyItem = () => {
    // 设定复制源
    var items = [];
    for (var key in selectedComponentMap.value) {
        var item = deepCopy(selectedComponentMap.value[key]);
        //item.identifiers=identifiers;
        items.push(item);
    }
    counterStore.setCopySrcItems(items);
}
const pasteItem = () => {
    // console.log("粘贴",this.copySrcItems);
    if (copySrcItems && copySrcItems.length > 0) {
        counterStore.execute({
            op: 'copy-add',
            items: copySrcItems,
        });
    }
}
const removeItem = (index?: any, component?: any) => {
    //移除组件
    counterStore.execute({
        op: 'del',
        index: index,
    });
    setLayerSelected(true);
}
// 设置选中的组件
const clearSelected = (component?: any) => {
    counterStore.clearSelectedComponent(component);
};
const setLayerSelected = (bool: any) => {
    counterStore.setLayerSelected(bool)
}
// 保存组态数据
// 保存组态数据
const saveZtDatas = async (): Promise<boolean> => {
    try {
        // 模拟延迟，确保 loading 先显示
        await new Promise((resolve) => setTimeout(resolve, 500));

        const canvasBox = document.querySelector('#surface-edit-layer');
        if (!canvasBox) {
            throw new Error('Canvas element not found');
        }
        const options = {
            backgroundColor: null,
            useCORS: true,
        };
        const canvas = await html2canvas(canvasBox as any, options);
        const base64 = canvas.toDataURL('image/png');
        const json = JSON.stringify(configData.value);
        const params = {
            guid: guid.value,
            base64,
            scadaData: json,
        };

        // 调用保存数据 API
        const res = await saveDetailData(params);
        if (res.code === 200) {
            const resolution = `${configData.value.layer.width}x${configData.value.layer.height}`;
            const params2 = { id: id.value, pageResolution: resolution };

            // 调用更新中心数据 API
            const updateRes = await updateCenter(params2);
            if (updateRes.code === 200) {
                // 假设这里是触发 Vue 事件总线
                // this.$busEvent.$emit('updateCenter');
                return true;
            } else {
                throw new Error('Failed to update center');
            }
        } else {
            throw new Error('Failed to save detail data');
        }
    } catch (error) {
        console.error(error);
        return false;
    }
};
// 监听 Ctrl + S 事件
const handleCrtlS = (e: KeyboardEvent): void => {
    // 获取按键的键码
    const key = e.key === 'S' || e.key === 's';
    if (key && e.ctrlKey) {
        loadingInstance.value = ElLoading.service({
            text: '请稍候...',
        });

        saveZtDatas().then((flag) => {
            if (flag) {
                loadingInstance.value.close();
                ElMessage.success('保存成功');
            }
        });
        e.preventDefault(); // 阻止浏览器默认的保存行为
    }
};
// 点击组件
const clickComponent = (index: any, component: any, event: any) => {
    //点击了ctrl
    if (event.ctrlKey == true) {
        setLayerSelected(false);
        if (selectedComponentMap[component.identifier] == undefined) {
            counterStore.addSelectedComponent(component);
        } else {
            counterStore.removeSelectedComponent(component);
        }
    } else {
        //这里不再处理点击事件，改为鼠标左键
        //this.clickItem(component,index);
    }
}
// 获取组态详情
const getZtDetails = () => {
    const guids = guid.value as any
    getByGuid(guids).then((res) => {
        console.log('组态详情', res.data.data);
        
        deviceZtRow.value = res.data.data;
        // document.title = deviceZtRow.value.pageName; // 修改title值
        if (deviceZtRow.value.scadaData) {
            let configData = JSON.parse(deviceZtRow.value.scadaData);
            console.log('组件JSON格式化', JSON.parse(deviceZtRow.value.scadaData));
            counterStore.loadDefaultTopoData(configData)
        } else {
            let configData = { name: '--', layer: { backColor: '', backgroundImage: '', widthHeightRatio: '', width: 1920, height: 1080 }, components: [] };
            counterStore.loadDefaultTopoData(configData)
        }
    });
}
const loadDefaultTopoData = (ctx: any, jsonData?: any) => {
    ctx.state.topoData = jsonData;
};
// 初始化组态
const initZtTab = () => {
    getZtDetails(); // 获取组态详情
    configDataHis.value = [];
    selectFlag.value = 0;
    clearSelected(); // 清掉选择的组件
    setLayerSelected(true); // 默认切换回背景设置
    emit('recoveryFlagClick', true); // 复位恢复标志
    emit('revokeFlagClick', true); // 复位撤销标志
}
// 获取所有组态数据
const getZtCenter = () => {
    return new Promise((resolve, reject) => {
        listCenter().then((res) => {
            if (res.data.code === 200) {
                ztList = res.data.rows;
                resolve(true);
            } else {
                reject(false);
            }
        });
    });
}
// 使用 computed 创建计算属性
const selectedComponents = computed(() => counterStore.selectedComponents,) as any;
const selectedComponentMap = computed(() => counterStore.selectedComponentMap,) as any;
const configData = computed(() => counterStore.topoData,) as any;
const selectedIsLayer = computed(() => counterStore.selectedIsLayer,) as any;
const copySrcItems = computed(() => counterStore.copySrcItems,) as any;
const copyCount = computed(() => counterStore.copyCount,) as any;
const sidebarStatus = computed(() => appStore.sidebar.opened) as any;
const layerStyle = computed(() => {
    const scale = zoom.value / 100;
    const styles = [`transform:scale(${scale})`];
    if (configData.layer.backColor) {
        styles.push(`background-color: ${configData.layer.backColor}`);
    }
    if (configData.layer.backgroundImage) {
        styles.push(`background-image: url("${configData.layer.backgroundImage}")`);
    }
    if (configData.layer.width > 0) {
        styles.push(`width: ${configData.layer.width}px`);
    }
    if (configData.layer.height > 0) {
        styles.push(`height: ${configData.layer.height}px`);
    }
    styles.push('overflow:hidden');
    return styles.join(';');
});
// 页面加载时
onMounted(() => {
    id.value = route.query.id, // 组态id
        guid.value = route.query.guid, // 组态标识
        console.log(route.query, 'route.query');

    document.addEventListener('keydown', handleCrtlS, false); // crtl+s保存数据
    initZtTab(); // 初始组态数据
    getZtCenter(); // 获取buttonTabs列表
});
// 在组件卸载时移除事件监听
onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleCrtlS);
});
</script>
<style lang="scss" scoped>
.topo-main-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    overflow-x: hidden;
    overflow-y: hidden;

    .content-wrap {
        height: calc(100% - 41px);
        width: 100%;

        .topo-layer {
            width: 100%;
            height: 100%;
            position: absolute;
            transform-origin: left top;
            overflow: auto;
            background-color: #ffffff;
            background-clip: padding-box;
            background-origin: padding-box;
            background-repeat: no-repeat;
            background-size: 100% 100%;

            .topo-frame-selection {
                background-color: #8787e7;
                opacity: 0.3;
                border: #0000ff solid 1px;
                position: absolute;
                z-index: 999;
            }

            .topo-layer-view-selected-line {
                outline: 1px solid #0cf;
            }

            .topo-layer-view {
                position: absolute;
                height: 100px;
                width: 100px;
                background-color: #999;
                cursor: move;
                // border: #ccc solid 1px;

                .resize-left-top {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    left: -5px;
                    top: -5px;
                    cursor: nwse-resize;
                }

                .resize-left-center {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    left: -5px;
                    top: 50%;
                    margin-top: -5px;
                    cursor: ew-resize;
                }

                .resize-left-bottom {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    left: -5px;
                    bottom: -5px;
                    cursor: nesw-resize;
                }

                .resize-right-top {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    right: -5px;
                    top: -5px;
                    cursor: nesw-resize;
                }

                .resize-right-center {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    right: -5px;
                    top: 50%;
                    margin-top: -5px;
                    cursor: ew-resize;
                }

                .resize-right-bottom {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    right: -5px;
                    bottom: -5px;
                    cursor: nwse-resize;
                }

                .resize-center-top {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    top: -5px;
                    left: 50%;
                    margin-left: -5px;
                    cursor: ns-resize;
                }

                .resize-center-bottom {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    bottom: -5px;
                    left: 50%;
                    margin-left: -5px;
                    cursor: ns-resize;
                }
            }

            .topo-layer-view-selected {
                border: 1.5px solid #21cc96;
            }

            .topo-layer-view-selected:hover {
                border: 1.5px dashed #21cc96;
            }
        }

        .topo-layer::before,
        .topo-layer::after {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            content: '';
            background-repeat: repeat;
            pointer-events: none;
        }

        .topo-layer::before {
            background-image: linear-gradient(to right, black 1px, transparent 1px, transparent 10px), linear-gradient(to bottom, black 1px, transparent 1px, transparent 10px);
            background-size: 10px 10px;
            opacity: 0.05;
        }

        .topo-layer::after {
            background-image: linear-gradient(to right, black 1px, transparent 1px, transparent 50px), linear-gradient(to bottom, black 1px, transparent 1px, transparent 50px);
            background-size: 50px 50px;
            opacity: 0.1;
        }
    }

    .footer-wrap {
        position: relative;
        height: 42px;
        border-top: 1px solid #d8dce5;
        background-color: #f1f3f4;
        display: flex;
        flex-direction: row;
        align-items: center;

        .left-wrap {
            flex: 1;

            ::v-deep .el-tabs__nav {
                border: unset;
                border-right: 1px solid #dfe4ed;
                border-radius: unset;
            }

            ::v-deep .is-focus {
                box-shadow: unset;
                border-radius: unset;
            }

            .dropdown-item {
                font-size: 12px;
            }

            .dropdown-item-active {
                font-size: 12px;
                color: #409eff;
            }
        }

        .right-wrap {
            height: 42px;
            width: 42px;
            line-height: 42px;
            text-align: center;
            cursor: pointer;
            font-size: 18px;
            color: #78797a;
            font-weight: 700;
            margin-right: 5px;
        }
    }
}
</style>
<style scoped>
.custom-class .menu_item__available:hover,
.custom-class .menu_item_expand {
    background: #ffecf2 !important;
    color: #ff4050 !important;
}
</style>
<style>
::-webkit-scrollbar-thumb {
    background-color: #e1e1e1;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #a5a2a2;
}

::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    position: absolute;
}

::-webkit-scrollbar-track {
    background-color: #fff;
}
</style>