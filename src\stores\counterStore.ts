// src/stores/counterStore.ts
import { defineStore } from 'pinia'
import Vue, { nextTick } from 'vue';
import uid from '/@/utils/uid.js';
import { deepCopy } from '/@/utils/index';
// 组件类型定义（假设你的组件结构是这样，按需调整）
interface Component {
  identifier: string;
  name: string;
  type: string;
  style: {
    visible: boolean;
    transform: number;
    borderWidth: number;
    borderStyle: string;
    borderColor: string;
    position: {
      x: number;
      y: number;
    };
  };
}
export const useCounterStore = defineStore('counter', {
  state: () => ({
    topoData: {
      name: '--',
      layer: {
        backColor: '',
        backgroundImage: '',
        widthHeightRatio: '',
        width: 1600,
        height: 900,
      },
      components: [] as Component[], // 将 components 强类型为 Component[]
    },
    selectedIsLayer: true,
    selectedComponent: null as Component | null, // selectedComponent 的类型是 Component 或 null
    selectedComponents: [] as string[], // selectedComponents 应该是一个字符串数组
    selectedComponentMap: {} as { [key: string]: Component }, // selectedComponentMap 的键为字符串，值为 Component
    copySrcItems: [] as any[], // copySrcItems 可以根据实际情况进一步强类型
    copyCount: 0,
    undoStack: [] as any[], // 根据实际情况强类型
    redoStack: [] as any[], // 根据实际情况强类型
    mqttData: {} as any, // 根据实际情况强类型
    historyData: [] as any[], // 根据实际情况强类型
  }),
  actions: {
    loadDefaultTopoData(jsonData:any) {
      this.topoData = jsonData;
    },
    async execute(command: any) {
      const { topoData, undoStack } = this;

      switch (command.op) {
        case 'add': {
          const component = command.component;
          const fuid = uid;
          component.identifier = fuid();
          component.name = `${component.type}${topoData.components.length}`;
          component.style = {
            ...component.style,
            visible: true,
            transform: 0,
            borderWidth: component.style.borderWidth || 0,
            borderStyle: component.style.borderStyle || 'solid',
            borderColor: component.style.borderColor || '#ccccccff',
          };
          topoData.components.push(component);
          break;
        }

        case 'del': {
          const keys = topoData.components
            .filter((component) => this.selectedComponentMap[component.identifier] !== undefined)
            .map((_, idx) => idx);

          keys.sort((a, b) => a - b).reverse().forEach((idx) => topoData.components.splice(idx, 1));
          break;
        }

        case 'move': {
          const { dx, dy, items } = command;
          items.forEach((item:any) => {
            item.style.position.x += dx;
            item.style.position.y += dy;
          });
          break;
        }

        case 'copy-add': {
          this.clearSelectedComponent();
          const identifiers = command.items.map(() => uid());
          for (const [i, item] of command.items.entries()) {
            let component = deepCopy(item);
            component.identifier = identifiers[i];
            component.name = `${component.type}${topoData.components.length}`;
            component.style = {
              ...component.style,
              visible: true,
              position: {
                x: component.style.position.x + 25,
                y: component.style.position.y + 25,
              },
            };
            topoData.components.push(component);
            this.addSelectedComponent(component);
            this.increaseCopyCount();
          }
          break;
        }

        default:
          console.warn('不支持的命令.');
      }

      // 异步操作处理，Pinia 不支持 `this.$nextTick`
      await nextTick();  // 使用 Vue 3 的 `nextTick`
      undoStack.push(command); // 将操作添加到撤销栈
    },

    async undo() {
      const command = this.undoStack.pop();
      if (!command) return;

      switch (command.op) {
        case 'add': {
          const component = command.component;
          const index = this.topoData.components.findIndex(
            (comp) => comp.identifier === component.identifier
          );
          if (index !== -1) this.topoData.components.splice(index, 1);
          break;
        }

        case 'move': {
          const { dx, dy } = command;
          for (const key in this.selectedComponentMap) {
            const component = this.selectedComponentMap[key];
            component.style.position.x -= dx;
            component.style.position.y -= dy;
          }
          break;
        }

        default:
          break;
      }

      // 异步操作处理，Pinia 不支持 `this.$nextTick`
      await nextTick();  // 使用 Vue 3 的 `nextTick`
    },

    async redo() {
      const command = this.redoStack.pop();
      if (!command) return;

      await this.execute(command); // 调用 execute 来处理重做操作
    },

    setSelectedComponent(component: any) {
      const fuid = uid;
      if (!component.identifier) {
        component.identifier = fuid();
      }
      this.selectedComponents = [component.identifier];
      this.selectedComponentMap = { [component.identifier]: component };
      this.selectedComponent = component;
    },

    addSelectedComponent(component: any) {
      const fuid = uid;
      if (!component.identifier) {
        component.identifier = fuid();
      }
      if (this.selectedComponentMap[component.identifier]) {
        return;
      }
      this.selectedComponents.push(component.identifier);
      this.selectedComponentMap[component.identifier] = component;
      this.selectedComponent = component;
    },

    removeSelectedComponent(component: any) {
      if (!component.identifier) return;
      const index = this.selectedComponents.indexOf(component.identifier);
      if (index > -1) {
        this.selectedComponents.splice(index, 1);
      }
      delete this.selectedComponentMap[component.identifier];
      if (this.selectedComponent && component.identifier === this.selectedComponent.identifier) {
        this.selectedComponent = null;
      }
      if (this.selectedComponents.length === 1) {
        const _component = this.selectedComponentMap[this.selectedComponents[0]];
        this.selectedComponent = _component;
      }
    },

    clearSelectedComponent() {
      this.selectedComponents = [];
      this.selectedComponentMap = {};
      this.selectedComponent = null;
    },

    setLayerSelected(selected: boolean) {
      this.selectedIsLayer = selected;
    },

    setCopySrcItems(items: any) {
      this.copySrcItems = items;
      this.copyCount = 0;
    },

    increaseCopyCount() {
      this.copyCount++;
    },

    setMqttData(mqttData: any) {
      this.mqttData = mqttData;
    },
  }
  

})
