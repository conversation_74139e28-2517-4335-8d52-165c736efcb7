// topoUtil.ts

interface Component {
    type: string;
  }
  
  interface EchartOption {
    series: Array<{ [key: string]: any }>;
  }
  
  interface EchartData {
    xdata: string[];
    ydata: number[];
  }
  
  export const topoUtil = {
    // 手动映射类型和组件的关系
    viewRegisterMap: {
      triangle: 'view-triangle',
      rect: 'view-rect',
      circular: 'view-circular',
      line: 'view-line',
      'line-arrow': 'view-line-arrow',
      'chart-line': 'view-chart',
      'chart-line-step': 'view-chart',
      'chart-bar': 'view-chart',
      'chart-pie': 'view-chart-pie',
      'chart-gauge': 'view-chart-gauge',
      'chart-water': 'view-chart-water',
      dashed: 'view-dashed',
      map: 'view-map',
    },
  
    // 优先匹配map，否则将自动匹配
    parseViewName(component: Component): string {
      let viewName = topoUtil.viewRegisterMap[component.type as keyof typeof topoUtil.viewRegisterMap];
      if (viewName === undefined) {
        // 如果没有手动配置映射，自动根据数据类型匹配
        viewName = 'view-' + component.type;
      }
      return viewName;
    },
  
    // 解析图表类型
    parseEchartType(component: Component, option: EchartOption) {
      delete option.series[0]['step'];
      delete option.series[0]['smooth'];
      if (component.type === 'chart-line') {
        option.series[0].type = 'line';
        option.series[0]['smooth'] = true;
      } else if (component.type === 'chart-bar') {
        option.series[0].type = 'bar';
      } else if (component.type === 'chart-line-step') {
        option.series[0].type = 'line';
        option.series[0].step = 'start';
      }
    },
  
    // 判断两个值的大小关系
    judgeSize(formula: string, value1: number, value2: number): boolean {
      let isTrue = false;
      if (formula === '大于') {
        isTrue = value1 > value2;
      } else if (formula === '大于等于') {
        isTrue = value1 >= value2;
      } else if (formula === '等于') {
        isTrue = value1 === value2;
      } else if (formula === '小于等于') {
        isTrue = value1 <= value2;
      } else if (formula === '小于') {
        isTrue = value1 < value2;
      } else if (formula === '不等于') {
        isTrue = value1 !== value2;
      }
      return isTrue;
    },
  
    // 补零操作
    add0(m: number): string {
      return m < 10 ? '0' + m : m.toString();
    },
  
    // 获取当前时间
    getNowTime(): string {
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const hour = date.getHours();
      const minute = date.getMinutes();
      const second = date.getSeconds();
      return `${year}-${this.add0(month)}-${this.add0(day)} ${this.add0(hour)}:${this.add0(minute)}:${this.add0(second)}`;
    },
  
    // 获取当前时间前指定小时的时间
    getTime(val: number): string {
      const frontTime = new Date(new Date().getTime() - val * 60 * 60 * 1000);
      const year = frontTime.getFullYear();
      const month = frontTime.getMonth() + 1;
      const day = frontTime.getDate();
      const hour = frontTime.getHours();
      const minute = frontTime.getMinutes();
      const second = frontTime.getSeconds();
      return `${year}-${this.add0(month)}-${this.add0(day)} ${this.add0(hour)}:${this.add0(minute)}:${this.add0(second)}`;
    },
  
    // 计算公式
    checkData(formula: string): number {
      let nums: string[] = [];
      let input = formula;
      let result = 0;
      const operate1 = ['*', '/'];
      const operate2 = ['+', '-'];
      const operate3 = ['%'];
  
      const calc1 = (str: string): number => {
        let n = 0;
        for (let i = 0; i < str.length; i++) {
          const element = str[i];
          if (operate1.indexOf(element) !== -1 || operate2.indexOf(element) !== -1 || operate3.indexOf(element) !== -1) {
            if (element === '*') {
              result = parseFloat(nums[n]) * parseFloat(nums[n + 1]);
              input = input.replace(nums[n] + '*' + nums[n + 1], '' + result);
              nums.splice(n, 2, result as any);
              n--;
            } else if (element === '/') {
              result = parseFloat(nums[n]) / parseFloat(nums[n + 1]);
              input = input.replace(nums[n] + '/' + nums[n + 1], '' + result);
              nums.splice(n, 2, result as any);
              n--;
            }
            n++;
          }
        }
        return result;
      };
  
      const calc2 = (str: string): number => {
        let n = 0;
        for (let i = 0; i < str.length; i++) {
          const element = str[i];
          if (operate1.indexOf(element) !== -1 || operate2.indexOf(element) !== -1 || operate3.indexOf(element) !== -1) {
            if (element === '+') {
              result = parseFloat(nums[n]) + parseFloat(nums[n + 1]);
              input = input.replace(nums[n] + '+' + nums[n + 1], '' + result);
              nums.splice(n, 2, result as any);
              n--;
            } else if (element === '-') {
              result = parseFloat(nums[n]) - parseFloat(nums[n + 1]);
              input = input.replace(nums[n] + '-' + nums[n + 1], '' + result);
              nums.splice(n, 2, result as any);
              n--;
            }
            n++;
          }
        }
        return result;
      };
  
      const calc3 = (str: string): number => {
        let n = 0;
        for (let i = 0; i < str.length; i++) {
          const element = str[i];
          if (operate1.indexOf(element) !== -1 || operate2.indexOf(element) !== -1 || operate3.indexOf(element) !== -1) {
            if (element === '%') {
              result = parseFloat(nums[n]) % parseFloat(nums[n + 1]);
              input = input.replace(nums[n] + '%' + nums[n + 1], '' + result);
              nums.splice(n, 2, result as any);
              n--;
            }
            n++;
          }
        }
        return result;
      };
  
      input.split(/[%*/+-]/).forEach((element) => {
        nums.push(element);
      });
      result = calc3(input);
      result = calc1(input);
      result = calc2(input);
  
      return result;
    },
  
    // 计算相隔的分钟数
    getMinutes(date: Date): number {
      const date1 = new Date().getTime();
      const date2 = date.getTime();
      const date3 = Math.floor(date1 - date2);
      const leave1 = date3 % (24 * 3600 * 1000);
      const leave2 = leave1 % (3600 * 1000);
      return Math.floor(leave2 / (60 * 1000));
    },
  
    // 获取CSS动画的keyframes
    getKeyframes(keyframesName: string): any {
      const styleSheet = document.styleSheets;
      let animation: any = {};
      for (let i = 0; i < styleSheet.length; i++) {
        try {
          for (let j = 0; j < styleSheet[i].cssRules.length; j++) {
            if (styleSheet[i].cssRules[j].constructor.name === 'CSSKeyframesRule') {
              const keyframesRule = styleSheet[i].cssRules[j] as CSSKeyframesRule;
              if (keyframesRule.name.indexOf(keyframesName) > -1) {
                animation.cssRules = styleSheet[i].cssRules[j];
                animation.index = j;
                animation.styleSheet = styleSheet[i];
                return animation;
              }
            }
          }
        } catch (error) {
          // 错误处理
        }
      }
    },
  
    // 获取Echart图表的数据说明
    getEchartExplain(): string {
      return `
        <p><span style="color: rgb(255, 153, 0);">响应示例</span><span style="color: rgb(0, 102, 204);">-</span><a href="https://www.isqqw.com/" rel="noopener noreferrer" target="_blank" style="background-color: rgb(255, 255, 255); color: rgb(0, 102, 204);">echartData</a></p>
        <p>{</p>
        <p>&nbsp;"msg": "操作成功",</p>
        <p>&nbsp;"code": 200,</p>
        <p>&nbsp;"<span style="color: rgb(0, 102, 204);">data</span>": {</p>
        <p>&nbsp;&nbsp;"<span style="color: rgb(0, 102, 204);">xdata</span>": ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],</p>
        <p>&nbsp;&nbsp;"<span style="color: rgb(0, 102, 204);">ydata</span>": [150, 230, 224, 218, 135, 147, 260]</p>
        <p>&nbsp;&nbsp;}</p>
        <p>}</p>
        <p><span style="color: rgb(255, 153, 0);">代码视图</span></p>
        <p>option = {</p>
        <p>&nbsp;xAxis: {</p>
        <p>&nbsp;&nbsp;	type: "category",</p>
        <p>&nbsp;	data: <a href="https://www.isqqw.com/" rel="noopener noreferrer" target="_blank" style="color: rgb(0, 102, 204); background-color: rgb(255, 255, 255);">echartData</a><span style="color: rgb(0, 102, 204);">.data.xdata</span></p>
        <p>&nbsp;},</p>
        <p>&nbsp;yAxis: {</p>
        <p>&nbsp;&nbsp;	type: "value"</p>
        <p>&nbsp;},</p>
        <p>&nbsp;series: [</p>
        <p>&nbsp;&nbsp;{</p>
        <p>&nbsp;&nbsp;&nbsp;	data: <a href="https://www.isqqw.com/" rel="noopener noreferrer" target="_blank" style="color: rgb(0, 102, 204); background-color: rgb(255, 255, 255);">echartData</a><span style="color: rgb(0, 102, 204);">.data.ydata</span>,</p>
        <p>&nbsp;&nbsp;&nbsp;	type: "line"</p>
        <p>&nbsp;&nbsp;}</p>
        <p>&nbsp;]</p>
        <p>};</p>
      `;
    },
  };
  
  export default topoUtil;
  