<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-form ref="queryForm" :inline="true" label-width="68px" style="margin-bottom:-20px;">
                <el-form-item label="设备名称" prop="deviceName">
                    <el-input v-model="state.tableData.param.deviceName" placeholder="请输入产品名称" clearable
                        size="default" />
                </el-form-item>
                <el-form-item label="设备编号" prop="serialNumber">
                    <el-input v-model="state.tableData.param.serialNumber" placeholder="请输入产品分类名称" clearable
                        size="default" />
                </el-form-item>
                <el-form-item label="设备状态" prop="status">
                    <el-select style="width: 240px" v-model="state.tableData.param.status" placeholder="请选择设备状态"
                        clearable size="default">
                        <el-option v-for="dict in device_status_list" :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" />
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="我的分组" prop="status">
                    <el-select style="width: 240px" v-model="state.tableData.param.groupId" placeholder="请选择我的分组"
                        clearable size="default">
                        <el-option v-for="group in myGroupList" :key="group.groupId" :label="group.groupName"
                            :value="group.groupId" />
                    </el-select>
                </el-form-item> -->
                <el-form-item>
                    <el-button size="default" type="primary" class="ml10" @click="getTableData">
                        <el-icon>
                            <ele-Search />
                        </el-icon>
                        查询
                    </el-button>
                    <el-button size="default" @click="resetQuery">
                        <el-icon><ele-Refresh /></el-icon>
                        重置
                    </el-button>
                </el-form-item>
                <el-form-item style="float:right;">
                    <!-- <el-button type="primary" plain size="default" @click="handleEditDevice(0)"
                        v-auths="['iot:product:add']"><el-icon><ele-Plus /></el-icon>新增</el-button> -->
                    <el-dropdown @command="(command: any) => handleCommand(command)" v-auths="['iot:device:add']">
                        <el-button size="default" type="primary" plain>
                            新增设备
                            <el-icon>
                                <ArrowDown />
                            </el-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="handleEditDevice">
                                    手动添加</el-dropdown-item>
                                <el-dropdown-item command="handleBatchImport">
                                    批量导入</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>

                    </el-dropdown>
                    <el-button style="margin-left: 15px;" type="primary" plain size="default"
                        @click="handleChangeShowType"><el-icon><ele-Grid /></el-icon>切换</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card style=" margin-top: 10px" shadow="hover">
            <el-row>
                <el-col :span="4">
                    <el-input v-model="groupName" size="default" placeholder="请输入分类名称" style="max-width: 90%">
                    </el-input>
                    <div class="mt10">
                        <el-tree :data="treeState.tableData.data" :props="defaultProps" :expand-on-click-node="false"
                            :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all
                            highlight-current @node-click="handleNodeClick" />
                    </div>
                </el-col>
                <el-col :span="20" :xs="24">
                    <div v-if="showType == 'list'">
                        <el-table v-loading="state.tableData.loading" :data="deviceList" border style="width: 100%"
                            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                            <el-table-column label="编号" align="center" header-align="center" prop="deviceId" />
                            <el-table-column label="设备名称" align="center" header-align="center" prop="deviceName"
                                min-width="50" />
                            <el-table-column label="设备编号" align="center" prop="serialNumber" width="150" />
                            <el-table-column label="所属产品" align="center" prop="productName" width="120" />
                            <!-- <el-table-column label="协议" align="center" prop="transport" width="100" />
                            <el-table-column label="通讯协议" align="center" prop="protocolCode" min-width="100" /> -->
                            <!-- <el-table-column label="子设备数" align="center" prop="subDeviceCount" width="100">
                                <template #default="scope">
                                    {{ scope.row.subDeviceCount }}
                                </template>
                            </el-table-column> -->
                            <!-- <el-table-column label="设备影子" align="center" prop="isShadow" width="100">
                                <template #default="scope">
                                    <el-tag type="success" size="small" v-if="scope.row.isShadow == 1">启用</el-tag>
                                    <el-tag type="info" size="small" v-else>禁用</el-tag>
                                </template>
                            </el-table-column> -->
                            <el-table-column label="状态" align="center" prop="status" width="100">
                                <template #default="scope">
                                    <dict-tag :options="device_status_list" :value="scope.row.status" size="small" />
                                </template>
                            </el-table-column>
                            <!-- <el-table-column label="信号" align="center" prop="rssi" width="80">
                                <template #default="scope">
                                    <svg-icon v-if="scope.row.status == 3 && scope.row.rssi >= '-55'"
                                        icon-class="wifi_4" />
                                    <svg-icon
                                        v-else-if="scope.row.status == 3 && scope.row.rssi >= '-70' && scope.row.rssi < '-55'"
                                        icon-class="wifi_3" />
                                    <svg-icon
                                        v-else-if="scope.row.status == 3 && scope.row.rssi >= '-85' && scope.row.rssi < '-70'"
                                        icon-class="wifi_2" />
                                    <svg-icon
                                        v-else-if="scope.row.status == 3 && scope.row.rssi >= '-100' && scope.row.rssi < '-85'"
                                        icon-class="wifi_1" />
                                    <svg-icon v-else icon-class="wifi_0" />
                                </template>
                            </el-table-column> -->
                            <!-- <el-table-column label="定位方式" align="center" prop="locationWay" width="100">
                                <template #default="scope">
                                    <dict-tag :options="location_way_list" :value="scope.row.locationWay"
                                        size="small" />
                                </template>
                            </el-table-column> -->
                            <el-table-column label="固件版本" align="center" prop="firmwareVersion" width="100">
                                <template #default="scope">
                                    <el-tag size="small" type="info">Ver {{ scope.row.firmwareVersion }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="激活时间" align="center" prop="activeTime" width="120">
                                <template #default="scope">
                                    <span>{{ parseTime(scope.row.activeTime, '{y}-{m}-{d}') }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="创建时间" align="center" prop="createTime" width="120">
                                <template #default="scope">
                                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                                </template>
                            </el-table-column>

                            <el-table-column label="操作" align="center" class-name="small-padding fixed-width"
                                width="250">
                                <template #default="scope">

                                    <el-button type="primary" size="small" style="padding: 5px"
                                        @click="handleEditDevice(scope.row, 'basic')"
                                        v-auths="['iot:device:query']"><el-icon><ele-View /></el-icon>查看</el-button>
                                    <el-button type="primary" size="small" style="padding: 5px"
                                        @click="openSummaryDialog(scope.row)" v-if="scope.row.deviceId != 0"
                                        v-auths="['iot:device:query']">二维码</el-button>
                                    <el-button type="danger" size="small" style="padding: 5px"
                                        @click="handleDelete(scope.row)"
                                        v-auths="['iot:device:remove']"><el-icon><ele-Delete /></el-icon>删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <!-- <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" :pageSizes="[12, 24, 36, 60]" @pagination="getList" /> -->

                        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
                            class="mt15" style="justify-content: flex-end;" :pager-count="5"
                            :page-sizes="[12, 24, 36, 60]" v-model:current-page="state.tableData.param.pageNum"
                            background v-model:page-size="state.tableData.param.pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total">
                        </el-pagination>
                    </div>
                    <div v-if="showType == 'card'">
                        <el-row :gutter="30" v-loading="state.tableData.loading">
                            <el-col v-for="(item, index) in deviceList" :key="index" :xs="24" :sm="12" :md="12"
                                :lg="8" :xl="6" style="margin-bottom: 30px; text-align: center;">
                                <el-card :body-style="{ padding: '20px' }" shadow="always" class="card-item">
                                    <el-row type="flex" :gutter="10" justify="space-between">
                                        <el-col :span="18" style="text-align: left">
                                            <el-link :underline="false" @click="handleDeviceDetail(item)"
                                                style="font-weight: bold; font-size: 16px; line-height: 32px">
                                                <div v-if="item.isOwner != 1">
                                                    <el-tooltip class="item" effect="dark" content="分享的设备"
                                                        placement="top-start">
                                                        <SvgIcon :name="'device'" :type="'menu'" :color="''" />
                                                    </el-tooltip>
                                                </div>
                                                <SvgIcon :name="'device'" :type="'menu'" :color="''"
                                                    v-if="item.isOwner == 1" />
                                                <span style="margin: 0 5px">{{ item.deviceName }}</span>
                                            </el-link>
                                        </el-col>
                                        <el-col :span="5" style=" cursor: pointer">
                                            <div style="display: flex;justify-content: space-evenly;">
                                                <div @click="openSummaryDialog(item)">
                                                    <SvgIcon :name="'qrcode'" :type="'menu'" :color="''"
                                                        v-auths="['iot:device:query']" />
                                                </div>

                                                <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''"
                                                    v-if="item.status == 3 && item.rssi >= '-55'" />
                                                <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''"
                                                    v-else-if="item.status == 3 && item.rssi >= '-70' && item.rssi < '-55'" />
                                                <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''"
                                                    v-else-if="item.status == 3 && item.rssi >= '-85' && item.rssi < '-70'" />
                                                <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''"
                                                    v-else-if="item.status == 3 && item.rssi >= '-100' && item.rssi < '-85'" />
                                                <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''" v-else
                                                    icon-class="wifi_0" />
                                            </div>
                                        </el-col>
                                        <!-- <el-col :span="1">
                                    <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''"
                                        v-if="item.status == 3 && item.rssi >= '-55'" />
                                    <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''"
                                        v-else-if="item.status == 3 && item.rssi >= '-70' && item.rssi < '-55'" />
                                    <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''"
                                        v-else-if="item.status == 3 && item.rssi >= '-85' && item.rssi < '-70'" />
                                    <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''"
                                        v-else-if="item.status == 3 && item.rssi >= '-100' && item.rssi < '-85'" />
                                    <SvgIcon :name="'wifi_4'" :type="'menu'" :color="''" v-else icon-class="wifi_0" />
                                    </el-col> -->
                                    </el-row>
                                    <el-row :gutter="10">
                                        <el-col :span="14">
                                            <div style="text-align: left; line-height: 40px; white-space: nowrap">
                                                <dictTag :options="device_status_list" :value="item.status" size="small"
                                                    style="display: inline-block" />
                                                <span style="display: inline-block; margin: 0 10px">
                                                    <!-- <el-tag type="success" size="small" v-if="item.isShadow == 1">影子</el-tag>
                                        <el-tag type="info" size="small" v-else>影子</el-tag> -->
                                                    <el-tag type="success" class="primary-tag" size="small"
                                                        v-if="item.protocolCode">{{
                                                            item.protocolCode
                                                        }}</el-tag>

                                                </span>
                                                <el-tag type="success" class="primary-tag" size="small"
                                                    v-if="item.transport">{{
                                                        item.transport
                                                    }}</el-tag>
                                                <!-- <dict-tag :options="dict.type.iot_location_way" :value="item.locationWay" size="small" style="display:inline-block;" /> -->
                                                <!-- <dict-tag :options="dict.type.iot_transport_type" :value="item.transport" size="small" style="display: inline-block" /> -->
                                            </div>
                                            <el-descriptions :column="1" size="small" style="white-space: nowrap">
                                                <el-descriptions-item label="编号">
                                                    {{ item.serialNumber }}
                                                </el-descriptions-item>
                                                <el-descriptions-item label="产品">
                                                    {{ item.productName }}
                                                </el-descriptions-item>
                                                <el-descriptions-item label="激活时间">
                                                    {{ parseTime(item.activeTime, '{y}-{m}-{d}') }}
                                                </el-descriptions-item>
                                            </el-descriptions>
                                        </el-col>
                                        <el-col :span="10">
                                            <div style="margin-top: 10px">
                                                <el-image style="width: 100%; height: 100px; border-radius: 10px" lazy
                                                    :preview-src-list="[baseUrl + item.imgUrl]"
                                                    :src="baseUrl + item.imgUrl" fit="cover"
                                                    v-if="item.imgUrl != null && item.imgUrl != ''"></el-image>
                                                <el-image style="width: 100%; height: 100px; border-radius: 10px"
                                                    :preview-src-list="[gateway]" :src="gateway" fit="cover"
                                                    v-else-if="item.deviceType == 2"></el-image>
                                                <el-image style="width: 100%; height: 100px; border-radius: 10px"
                                                    :preview-src-list="[video]" :src="video" fit="cover"
                                                    v-else-if="item.deviceType == 3"></el-image>
                                                <el-image style="width: 100%; height: 100px; border-radius: 10px"
                                                    :preview-src-list="[product]" :src="product" fit="cover"
                                                    v-else></el-image>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <el-button-group style="margin-top: 15px">
                                        <el-button type="danger" size="default" style="padding: 5px 10px"
                                            @click="handleDelete(item)"
                                            v-auths="['iot:device:remove']"><el-icon><ele-Delete /></el-icon>删除</el-button>
                                        <el-button type="primary" size="default" style="padding: 5px 15px"
                                            @click="handleEditDevice(item, 'basic')"
                                            v-auths="['iot:device:query']"><el-icon><ele-View /></el-icon>查看</el-button>
                                        <el-button type="success" size="default" style="padding: 5px 15px"
                                            @click="handleRunDevice(item)"
                                            v-auths="['iot:device:query']"><el-icon><ele-Odometer /></el-icon>运行状态</el-button>
                                    </el-button-group>
                                </el-card>
                            </el-col>
                        </el-row>

                        <el-empty description="暂无数据，请添加设备" v-if="state.tableData.total == 0"></el-empty>
                        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
                            class="mt15" style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                            v-model:current-page="state.tableData.param.pageNum" background
                            v-model:page-size="state.tableData.param.pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total">
                        </el-pagination>
                    </div>
                </el-col>
            </el-row>
        </el-card>
        <!-- 二维码 -->
        <el-dialog v-model="openSummary" width="400px" append-to-body>
            <div
                style="  border: 1px solid #ccc;display: flex;flex-direction: column;justify-content: center;align-items: center; padding: 20px;">
                <QrcodeVue :value="qrText" :size="200"></QrcodeVue>
                <!-- <div style="width: 200px; height: 200px; border: 1px solid;"></div> -->
                <div style="padding-top: 30px;">设备二维码</div>
            </div>
        </el-dialog>
        <DeviceDialog ref="DeviceDialogRef" @refresh="getTableData()" />
        <!-- 批量导入设备 -->
        <batchImport ref="batchImportRef" @save="getTableData()"></batchImport>
    </div>
</template>
<script setup lang="ts" name="">
import QrcodeVue from 'qrcode.vue';
import { reactive, onMounted, ref, onActivated, watch, defineAsyncComponent } from 'vue';
import { ElMessageBox, ElMessage, ElTree } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { listGroup } from '/@/api/iot/group';
import { useUserInfo } from '/@/stores/userInfo';
import { useRoute } from 'vue-router';
import { handleTree, parseTime } from '/@/utils/next'
import { delDevice, listDeviceShort } from '/@/api/iot/device';
import gateway from '/@/assets/images/gateway.png';
import video from '/@/assets/images/video.png';
import product from '/@/assets/images/product.png';
import router from '/@/router';
import mqttTool from '/@/utils/mqttTool';
import { delSipDeviceBySipId } from '/@/api/iot/sipdevice';
import { auths } from '/@/utils/authFunction';
import { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type';
const route = useRoute();
const userInfoStore = useUserInfo();
const dictStore = useDictStore();  // 使用 Pinia store
// 引入组件
const DeviceDialog = defineAsyncComponent(() => import('/@/views/iot/device/dialog.vue'));
const batchImport = defineAsyncComponent(() => import('/@/views/iot/device/batch-import-dialog.vue'));
// const DeviceDialog = defineAsyncComponent(() => import('/@/views/iot/device/dialog.vue'));

// 定义变量内容
const DeviceDialogRef = ref();
const batchImportRef = ref();

// 定义变量内容
const showType = ref('card')
const state = reactive<SysDicState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            deviceName: undefined,
            serialNumber: undefined,
            status: '',
            userId: undefined,
            productId: null,
            groupId: null,

        },
    },
});
const treeState = reactive({
    tableData: {
        data: [],
        // total: 0,
        loading: false,
        param: {
            // pageNum: 1,
            // pageSize: 10,
            groupName: undefined,//分类名称
            userId: undefined
        },
    },
});
const groupName = ref()   //树形空间名称
const tree = ref<InstanceType<typeof ElTree> | null>(null);  // 明确类型注解
const defaultProps = reactive({
    children: "children",
    label: "groupName"
});
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API)
// 是否管理员
const ids = ref() //groupId
interface productTypeOption {
    productName: any;
    isOwner: any;
    protocolCode: any;
    deviceName: any;
    rssi: any;
    productId: any;
    transport: any;
    imgUrl: any;
    status: any;
    deviceType: any;
    serialNumber: any;
    activeTime: any;
    isShadow: any
    deviceId: any;
    subDeviceCount: any
}
const deviceList = ref<productTypeOption[]>([]);
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
interface GroupOption {
    groupId: string;
    groupName: string;

}
const device_status_list = ref<TypeOption[]>([]);
const myGroupList = ref<GroupOption[]>([]);
const location_way_list = ref<TypeOption[]>([]);
const methodlist = ref<TypeOption[]>([]);
const chiplist = ref<TypeOption[]>([]);
// 二维码内容
const qrText = ref('fastbee')
// 打开设备配置对话框
const openSummary = ref(false)
/** 切换显示方式 */
const handleChangeShowType = () => {
    showType.value = showType.value == 'card' ? 'list' : 'card';
}
// 初始化表格数据
const getTableData = async () => {

    try {
        state.tableData.loading = true;
        const response = await listDeviceShort(state.tableData.param);
        state.tableData.total = response.data.total;
        deviceList.value = response.data.rows
        console.log(deviceList.value, 'deviceList.value');
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
// 初始化树状图
const getTreeData = async () => {
    try {
        const response = await listGroup(treeState.tableData.param);
        treeState.tableData.data = handleTree(response.data.data, "groupId") as any;
        console.log(treeState.tableData.data, 'treeState.tableData.data');

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {

    }
}
// 筛选节点
const filterNode = (value: string, data: TreeNodeData) => {
    if (!value) return true;
    return data.groupName.indexOf(value) !== -1;
}
// 树形控件节点单击事件
const handleNodeClick = (data: { groupId: any; }) => {
    state.tableData.param.groupId = data.groupId;
    state.tableData.param.pageNum = 1;
    getTableData();
}
/** 查询设备分组列表 */
// const getGroupList = () => {
//     this.loading = true;
//     let queryParams = {
//         pageSize: 30,
//         pageNum: 1,
//         userId: userInfoStore.userInfos.userId,
//     };
//     listGroup(queryParams).then((response) => {
//         this.myGroupList = response.rows;
//     });
// }
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        deviceName: undefined,
        serialNumber: undefined,
        status: '',
        userId: undefined
    }
}
// 获取状态数据 查询设备分组列表
const getdictdata = async () => {
    try {
        device_status_list.value = await dictStore.fetchDict('iot_device_status')
        location_way_list.value = await dictStore.fetchDict('iot_location_way')
        methodlist.value = await dictStore.fetchDict('iot_network_method')
        chiplist.value = await dictStore.fetchDict('iot_device_chip')
        let queryParams = {
            pageSize: 10,
            pageNum: 1,
            userId: userInfoStore.userInfos.userId,
        };
        listGroup(queryParams).then((response) => {
            myGroupList.value = response.data.rows;
        });
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
const openSummaryDialog = (row: any) => {
    console.log(row);

    let json = {
        type: 1, // 1=扫码关联设备
        deviceNumber: row.serialNumber,
        productId: row.productId,
        productName: row.productName,
    };
    qrText.value = JSON.stringify(json);
    openSummary.value = true;
}

// 点击名称查看
const handleDeviceDetail = (item: any) => {
    if (auths(['iot:device:query'])) {
        console.log(auths(['iot:device:query']));

        handleEditDevice(item, 'basic');
    }
}
/** 新增/查看按钮操作 */
const handleEditDevice = (row: any, activeName?: any) => {
    let deviceId = 0;
    let isSubDev = 0;
    if (row != 0 as any) {
        deviceId = row.deviceId || ids.value;
        isSubDev = row.subDeviceCount > 0 ? 1 : 0;
        router.push({
            path: '/iot/device-edit',
            query: {
                deviceId: deviceId,
                isSubDev: isSubDev,
                pageNum: state.tableData.param.pageNum,
                activeName: activeName,
            },
        });
    } else {
        DeviceDialogRef.value.openDialog();
    }

}
/** 运行状态按钮操作 */
const handleRunDevice = (row: any) => {
    let deviceId = 0;
    let isSubDev = 0;
    if (row != 0) {
        deviceId = row.deviceId || ids.value;
        isSubDev = row.subDeviceCount > 0 ? 1 : 0;
    }
    if (row.deviceType === 3) {
        router.push({
            path: '/iot/device-edit',
            query: {
                deviceId: deviceId,
                isSubDev: isSubDev,
                pageNum: state.tableData.param.pageNum,
                activeName: 'sipChannel',
            },
        });
    } else {
        router.push({
            path: '/iot/device-edit',
            query: {
                deviceId: deviceId,
                isSubDev: isSubDev,
                pageNum: state.tableData.param.pageNum,
                activeName: 'runningStatus',
            },
        });
    }
}
/** 删除按钮操作 */
const handleDelete = (row: { deviceId: any; deviceType: any; serialNumber: any; }) => {
    const deviceIds = row.deviceId || ids.value;
    ElMessageBox.confirm('是否确认删除产品编号为"' + deviceIds + '"的数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(function () {
            if (row.deviceType === 3) {
                delSipDeviceBySipId(row.serialNumber);
            }
            // 删除SIP配置
            delDevice(deviceIds).then(response => {
                getTableData();
                ElMessage.success('删除成功');
            });
        }).catch(() => { });
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
/* 连接Mqtt消息服务器 */
const connectMqtt = async () => {
    if (mqttTool.client == null) {
        await mqttTool.connect();
    }
    mqttCallback();
    getTableData();
    getdictdata()
    getTreeData()
}
/* Mqtt回调处理  */
const mqttCallback = () => {
    if (mqttTool.client) {
        mqttTool.client.on('message', (topic, message, buffer) => {
            let topics = topic.split('/');
            let productId = topics[1];
            let deviceNum = topics[2];
            const parsedMessage = JSON.parse(message.toString());
            if (!parsedMessage) {
                return;
            }
            if (topics[3] == 'status') {
                console.log('接收到【设备状态】主题：', topic);
                console.log('接收到【设备状态】内容：', parsedMessage);
                // 更新列表中设备的状态
                for (let i = 0; i < deviceList.value.length; i++) {
                    if (deviceList.value[i].serialNumber == deviceNum) {
                        deviceList.value[i].status = parsedMessage.status;
                        deviceList.value[i].isShadow = parsedMessage.isShadow;
                        deviceList.value[i].rssi = parsedMessage.rssi;
                        return;
                    }
                }
            }
        });
    }
}
// 新增设备更多操作触发
const handleCommand = (command: any) => {
    switch (command) {
        case 'handleEditDevice':
            handleEditDevice(0);
            break;
        case 'handleBatchImport':
            handleBatchImport();
            break;
        default:
            break;
    }
}
//批量导入设备
const handleBatchImport = () => {
    batchImportRef.value.upload.importDeviceDialog = true;
    batchImportRef.value.importForm.productName = null;
}
// 监听 deptName 的变化
watch(groupName, (val) => {
    // 确保树组件已挂载
    if (tree.value) {
        tree.value.filter(val);  // 调用树组件的 filter 方法
    }
});
// 页面加载时
onMounted(() => {
    // 产品筛选
    let productId = route.query.productId;

    if (productId != null) {
        state.tableData.param.productId = Number(productId);
        state.tableData.param.groupId = null;
        state.tableData.param.serialNumber = null;
    }
    // // 分组筛选
    let groupId = route.query.groupId;
    if (groupId != null) {
        state.tableData.param.groupId = Number(groupId);
        state.tableData.param.productId = null;
        state.tableData.param.serialNumber = null;
    }
    // // 设备编号筛选
    let sn = route.query.sn;
    if (sn != null) {
        state.tableData.param.serialNumber = sn;
        state.tableData.param.productId = null;
        state.tableData.param.groupId = null;
    }
    connectMqtt();

});
onActivated(() => {
    const time = route.query.t;
    if (time != null && time != uniqueId.value) {
        uniqueId.value = time;
        // 页码筛选
        let pageNum = route.query.pageNum;
        if (pageNum != null) {
            state.tableData.param.pageNum = Number(pageNum);
        }
        // 产品筛选
        let productId = route.query.productId;
        if (productId != null) {
            state.tableData.param.productId = Number(productId);
            state.tableData.param.groupId = null;
            state.tableData.param.serialNumber = null;
        }
        // 分组筛选
        let groupId = route.query.groupId;
        if (groupId != null) {
            state.tableData.param.groupId = Number(groupId);
            state.tableData.param.productId = null;
            state.tableData.param.serialNumber = null;
        }
        // 设备编号筛选
        let sn = route.query.sn;
        if (sn != null) {
            state.tableData.param.serialNumber = sn;
            state.tableData.param.productId = null;
            state.tableData.param.groupId = null;
        }
        getTableData();
        getdictdata()
    }
});
</script>
<style scoped>
.card-item {
    border-radius: 15px;
}

.primary-tag {
    background-color: #ebf5ff;
    /* 设置类似 primary 的蓝色背景 */
    color: #1890ff;
}

:deep(.el-descriptions__cell) {
    display: flex !important;
}
</style>