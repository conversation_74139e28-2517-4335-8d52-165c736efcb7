<template>
	<div class="big-data-up">
		<div class="up-left">
			<div class="content_tabs">
				<div class="tabs">
					<div class="tab" :class="activetab == 0 ? 'active' : ''" @click="changactiveTab(0)">
						<div class="span">
							<div class="item">运维看板</div>
						</div>
					</div>
					<div class="tab" :class="activetab == 1 ? 'active' : ''" @click="changactiveTab(1)">
						<div class="span">
							<div class="item">运维管理</div>
						</div>
					</div>
					<div class="tab" :class="activetab == 2 ? 'active' : ''" @click="changactiveTab(2)">
						<div class="span">
							<div class="item">系统管理</div>
						</div>
					</div>
					<div class="tab" :class="activetab == 3 ? 'active' : ''" @click="changactiveTab(3)">
						<div class="span">
							<div class="item">系统监控</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="up-center">
			<span>物联网运维管理平台</span>
		</div>
		<div class="header-img"></div>
		<div class="up-right" @click="emit('child-button-click')">
			<el-icon size="20">
				<FullScreen />
			</el-icon>
		</div>
	</div>
	<!-- <div class="big-data-up">
		<div class="up-left">
			<div class="title">
				<span>物联网运维管理平台</span>
			</div>
			<div class="content_tabs">
				<div class="tabs">
					<div class="tab active">
						<div class="span">
							<div class="item">运维看板</div>
						</div>
					</div>
					<div class="tab">
						<div class="span">
							<div class="item">运维管理</div>
						</div>
					</div>
					<div class="tab">
						<div class="span">
							<div class="item">系统管理</div>
						</div>
					</div>
					<div class="tab">
						<div class="span">
							<div class="item">系统监控</div>
						</div>
					</div>
				</div>
			</div>
		</div>

	</div> -->
</template>

<script setup lang="ts" name="chartHead">
import { reactive, onBeforeMount, onUnmounted, ref } from 'vue';
const emit = defineEmits(['child-button-click', 'showActive']);
import { formatDate } from '/@/utils/formatTime';
// 定义变量内容
const state = reactive({
	time: {
		txt: '',
		fun: 0,
	},
});
const activetab = ref(0);

// 初始化时间
const initTime = () => {
	state.time.txt = formatDate(new Date(), 'YYYY-mm-dd HH:MM:SS WWW QQQQ');
	state.time.fun = window.setInterval(() => {
		state.time.txt = formatDate(new Date(), 'YYYY-mm-dd HH:MM:SS WWW QQQQ');
	}, 1000);
};
const changactiveTab = (index: number) => {
	
	activetab.value = index;
	emit('showActive', activetab.value);
};
// const jump = () => {
// 	router.push("/screen/qqq");

// };
// 页面加载前
onBeforeMount(() => {
	initTime();
});
// 页面卸载时
onUnmounted(() => {
	window.clearInterval(state.time.fun);
});
</script>

<style scoped lang="scss">
.big-data-up {
	height: 12vh;
	/* 使用vh单位，使其相对于视口高度 */
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	// padding: 0 5vw; /* 使用vw单位，实现宽度自适应 */
	color: var(--el-color-white);
	overflow: hidden;
	position: relative;

	.up-left {
		position: absolute;
		font-size: 0.7vw;
		/* 使用相对单位，根据屏幕尺寸调整 */
		top: 55%;
		left: 0%;
		z-index: 9999;
	}

	.up-right {
		width: 1vw;
		height: 1vw;
		padding: 0;
		margin: 0;
		border: none;
		cursor: pointer;
		position: absolute;
		right: 1%;
		top: 20%;
	}

	.up-center {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 1.6vw;
		/* 使用相对单位，字体大小随屏幕变化 */
		font-weight: 500;
		letter-spacing: 0.5vw;
		/* 调整字母间距，使得文本更具可读性 */
		background-image: -webkit-linear-gradient(top,
				#6ac7ff,
				#bce6fe 25%,
				#97d4f8 50%,
				#74cbff 75%,
				#4b9fdc);
		-webkit-text-fill-color: transparent;
		-webkit-background-clip: text;
		background-clip: text;
		background-size: 200% 100%;
		-webkit-animation: masked-animation-data-v-b02d8052 4s linear infinite;
		animation: masked-animation-data-v-b02d8052 4s linear infinite;
		position: relative;

		@keyframes masked-animation {
			0% {
				background-position: 0 0;
			}

			100% {
				background-position: -100% 0;
			}
		}

		span {
			cursor: pointer;
		}
	}

	.header-img {
		background: url("./images/header1.png") no-repeat center center;
		background-size: cover;
		/* 背景图片自适应，覆盖整个区域 */
		height: 80%;
		/* 根据需要调整高度 */
		width: 82%;
		/* 根据需要调整宽度 */
		position: absolute;
		top: 12%;
	}
}

// .big-data-up {
// 	height: 8vh;
// 	/* 使用vh单位，使其相对于视口高度 */
// 	width: 100%;
// 	// display: flex;
// 	// align-items: center;
// 	// justify-content: center;
// 	padding: 0 1vw;
// 	/* 使用vw单位，实现宽度自适应 */
// 	color: var(--el-color-white);
// 	overflow: hidden;
// 	position: relative;

// 	.up-left {
// 		display: flex;
// 		justify-content: flex-start;
// 		align-items: center;
// 		width: 100%;
// 		height: 100%;
// 	}

// 	.title {
// 		// width: 100%;
// 		height: 100%;
// 		display: flex;
// 		justify-content: flex-start;
// 		align-items: center;
// 		font-size: 3.5rem;
// 		/* 使用相对单位，字体大小随屏幕变化 */
// 		font-weight: 500;
// 		letter-spacing: 0.5vw;
// 		/* 调整字母间距，使得文本更具可读性 */
// 		background-image: -webkit-linear-gradient(top,
// 				#6ac7ff,
// 				#bce6fe 25%,
// 				#97d4f8 50%,
// 				#74cbff 75%,
// 				#4b9fdc);
// 		-webkit-text-fill-color: transparent;
// 		-webkit-background-clip: text;
// 		background-clip: text;
// 		background-size: 200% 100%;
// 		-webkit-animation: masked-animation-data-v-b02d8052 4s linear infinite;
// 		animation: masked-animation-data-v-b02d8052 4s linear infinite;
// 		position: relative;

// 		@keyframes masked-animation {
// 			0% {
// 				background-position: 0 0;
// 			}

// 			100% {
// 				background-position: -100% 0;
// 			}
// 		}

// 		span {
// 			cursor: pointer;
// 		}
// 	}

.content_tabs {
	// position: absolute;
	// top: 20%;
	// left: 0;
	// font-size: 0.7vw;
	/* 使用相对单位，根据屏幕尺寸调整 */
	// width: 32vw;
	height: 100%;
	margin-left: 0.8rem;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	position: relative;

	.tabs {
		// width: 32vw;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 100%;

		// .tab {
		// 	// width: 25%;
		// 	height: 100%;
		// 	font-size: 2rem;
		// 	padding: 0 0.5vw;
		// 	display: flex;
		// 	align-items: center;
		// 	justify-content: center;
		// 	border-sizing: border-box;

		// }



		.tab {
			width: 25%;
			margin: 0.5rem 1rem;

			.span {
				// width: 8vw;
				display: inline-block;
				position: relative;
				// margin: 0.5rem 1rem;
				width: 100%;
				height: 100%;
				color: #ccc;

				.item {
					display: flex;
					font-size: 1.2rem;
					line-height: 40px;
					padding: 0 1.5rem;
					letter-spacing: 0.1rem;
					// position: relative;
				}

				.item:before {
					border-left: 2px solid #02a6b5;
					left: -0.1rem;
				}

				.item:after {
					border-right: 2px solid #02a6b5;
					right: -0.1rem;
				}

				.item:before,
				.item:after {
					position: absolute;
					width: 1rem;
					height: 0.5rem;
					opacity: .4;
					content: "";
					border-bottom: 2px solid #02a6b5;
					bottom: -0.1rem;
					border-radius: 2px;
				}

				// .item:hover:before,
				// .item:hover:after {
				// 	border-color: #22a5d8;
				// 	opacity: 1;
				// }

			}

			.span:before {
				border-left: 2px solid #02a6b5;
				left: -0.1rem;
			}

			.span:after {
				border-right: 2px solid #02a6b5;
				right: -0.1rem;
			}

			.span:before,
			.span:after {
				position: absolute;
				width: 1rem;
				height: 0.5rem;
				opacity: .4;
				content: "";
				border-top: 2px solid #02a6b5;
				top: -0.1rem;
				border-radius: 2px;
			}

			// .span:hover:before,
			// .span:hover:after {
			// 	border-color: #22a5d8;
			// 	opacity: 1;
			// }
			.span:hover {
				color: #ffffff;
				background: linear-gradient(to top, rgb(255, 255, 255, 0.1), rgb(255, 255, 255, 0), ) !important;
			}
		}

		.active {
			background: linear-gradient(to top, rgb(34, 165, 216, 0.2), rgb(34, 165, 216, 0), ) !important;

			// color: #ffffff !important;
			.span:before {
				border-color: #22a5d8;
				opacity: 1;
			}

			.span:after {
				border-color: #22a5d8;
				opacity: 1;
			}

			.span {
				color: #22a5d8 !important;
				;

				.item:before {
					border-color: #22a5d8;
					opacity: 1;
				}

				.item:after {
					border-color: #22a5d8;
					opacity: 1;
				}
			}



		}
	}
}


// }</style>