{"name": "vue-next-admin-template", "version": "2.4.33", "description": "vue3 vite next admin template", "author": "lyt_20201208", "license": "MIT", "scripts": {"dev": "vite --force", "build": "vite build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueup/vue-quill": "^1.2.0", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.3.5", "echarts": "^5.4.2", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.9.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^3.0.1", "mitt": "^3.0.1", "mqtt": "^5.10.4", "nprogress": "^0.2.0", "pinia": "^2.0.34", "qrcode.vue": "^3.6.0", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.11.1", "quill": "^2.0.3", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "three": "^0.176.0", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.13.11", "vue-grid-layout": "^2.4.0", "vue-i18n": "^10.0.5", "vue-router": "^4.1.6", "vue-ruler-tool": "^1.2.4", "vue3-ace-editor": "^2.2.4", "vue3-count-to": "^1.1.2", "vue3-json-viewer": "^2.2.2"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/node": "^18.15.11", "@types/nprogress": "^0.2.0", "@types/vue3-json-viewer": "^2.2.0", "@types/webpack-env": "^1.18.5", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vitejs/plugin-vue": "^4.1.0", "@vue/compiler-sfc": "^3.2.47", "esbuild-wasm": "^0.25.3", "eslint": "^8.38.0", "eslint-plugin-vue": "^9.10.0", "prettier": "^2.8.7", "sass": "^1.61.0", "typescript": "^5.0.4", "vite": "^4.2.1", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.1.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/lyt-top/vue-next-admin/issues"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "vuejs/vue-next-template", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/lyt-top/vue-next-admin.git"}}