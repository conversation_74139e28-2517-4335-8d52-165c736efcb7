import { createApp } from 'vue';
import pinia from '/@/stores/index';
import App from '/@/App.vue';
import router from '/@/router';
import { i18n } from '/@/i18n/index';
import { directive } from '/@/directive/index';
import VueGridLayout from 'vue-grid-layout';
import other from '/@/utils/other';
import globalComponent from '/@/components/index'
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';  // 导入所有图标
import '/@/theme/index.scss';
const app = createApp(App);

directive(app);
other.elSvg(app);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);  // 注册每个图标
  }
app.use(pinia).use(router).use(ElementPlus).use(i18n).use(VueGridLayout).mount('#app');
// 注册全局的组件
for (const componentItme in globalComponent) {
    app.component(componentItme, globalComponent[componentItme])
}