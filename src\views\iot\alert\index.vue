<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="告警名称" prop="alertName">
                        <el-input v-model="state.tableData.param.alertName" clearable size="default"
                            placeholder="请输入告警名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="告警级别" prop="alertLevel">
                        <el-select v-model="state.tableData.param.alertLevel" placeholder="请选择告警级别" clearable
                            size="default" style="width: 240px">
                            <el-option v-for="dict in alert_level_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                    <el-form-item style="float: right;">
                        <el-button plain v-auths="['iot:alert:add']" size="default" type="primary" class="ml5"
                            @click="handleAdd('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                    </el-form-item>

                </el-form>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column label="告警名称" align="center" prop="alertName" />
                <el-table-column label="状态" align="center" prop="status">
                    <template #default="scope">
                        <el-tag type="success" size="default" v-if="scope.row.status == 1">启动</el-tag>
                        <el-tag type="danger" size="default" v-if="scope.row.status == 0">暂停</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="告警级别" align="center" prop="alertLevel">
                    <template #default="scope">
                        <dict-tag :options="alert_level_list" :value="scope.row.alertLevel" size="default" />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="备注信息" align="center" prop="remark" />
                <el-table-column label="操作" align="center" class-name="default-padding fixed-width">
                    <template #default="scope">
                        <div style="display: flex;justify-content: center;">
                            <el-button size="default" type="primary" text @click="handleUpdate(scope.row)"
                                v-auths="['iot:alert:query']"><el-icon
                                    size="default"><ele-View /></el-icon>查看</el-button>
                            <el-button size="default" type="primary" text @click="handleDelete(scope.row)"
                                v-auths="['iot:alert:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <!-- 添加或修改设备告警对话框 -->
        <el-dialog style="position: absolute; top: 100px;" :title="dialogData.tableData.dialog.title"
            v-model="dialogData.tableData.dialog.isShowDialog" width="900px" append-to-body
            :close-on-click-modal="false" :close-on-press-escape="false">
            <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
            <el-form ref="DialogFormRef" :model="dialogData.tableData.ruleForm" :rules="rules" label-width="90px">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="告警名称" prop="alertName">
                            <el-input text v-model="dialogData.tableData.ruleForm.alertName" placeholder="请输入告警名称" />
                        </el-form-item>
                        <el-form-item label="告警级别" prop="alertLevel">
                            <el-select text v-model="dialogData.tableData.ruleForm.alertLevel" placeholder="请选择告警级别"
                                style="width: 100%">
                                <el-option v-for="dict in alert_level_list" :key="dict.dictValue"
                                    :label="dict.dictLabel" :value="parseInt(dict.dictValue)"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="备注信息" prop="remark">
                            <el-input v-model="dialogData.tableData.ruleForm.remark" type="textarea" placeholder="请输入内容"
                                :rows="1" />
                        </el-form-item>
                        <el-form-item label="告警状态">
                            <el-switch v-model="dialogData.tableData.ruleForm.status" :active-value="1"
                                :inactive-value="0" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <el-tabs v-model="activeName" style="padding: 10px">
                <el-tab-pane label="关联场景" name="relateScene">
                    <el-table :data="dialogData.tableData.ruleForm.scenes" border
                        v-loading="dialogData.tableData.dialog.sceneLoading" size="default">
                        <el-table-column prop="sceneName" align="center" label="场景名称"></el-table-column>
                        <el-table-column label="状态" align="center" prop="enable">
                            <template #default="scope">
                                <el-tag type="success" size="default" v-if="scope.row.enable == 1">启动</el-tag>
                                <el-tag type="danger" size="default" v-if="scope.row.enable == 2">暂停</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="触发条件" align="center" prop="cond">
                            <template #default="scope">
                                <span v-if="scope.row.cond == 1">任意条件</span>
                                <span v-if="scope.row.cond == 2">所有条件</span>
                                <span v-if="scope.row.cond == 3">不满足条件</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="执行方式" align="center" prop="executeMode">
                            <template #default="scope">
                                <span v-if="scope.row.executeMode == 1">串行</span>
                                <span v-if="scope.row.executeMode == 2">并行</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" width="120">
                            <template #default="scope">
                                <el-button size="default" text type="primary" @click="handleAlertSceneRemove(scope.row)"
                                    v-auths="['iot:alert:remove']"><el-icon>
                                        <Delete />
                                    </el-icon>移除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="消息通知" name="notify">
                    <el-table :data="dialogData.tableData.ruleForm.notifyTemplateList" border
                        v-loading="dialogData.tableData.dialog.notifyLoading" size="default">
                        <el-table-column prop="name" align="center" label="模板名称"></el-table-column>
                        <el-table-column label="状态" align="center" prop="status">
                            <template #default="scope">
                                <el-tag type="success" size="default" v-if="scope.row.status == '1'">启动</el-tag>
                                <el-tag type="danger" size="default" v-if="scope.row.status == '0'">暂停</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="渠道类型" align="center" prop="channelType">
                            <template #default="scope">
                                <dict-tag :options="channel_type_list" :value="scope.row.channelType" />
                            </template>
                        </el-table-column>
                        <el-table-column label="渠道账号" align="center" prop="channelName"></el-table-column>
                        <el-table-column label="服务商" align="center" prop="provider"></el-table-column>
                        <el-table-column label="操作" align="center" width="120">
                            <template #default="scope">
                                <el-button size="default" type="primary" text
                                    @click="handleAlertNotifyTempRemove(scope.row)"><el-icon>
                                        <Delete />
                                    </el-icon>移除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
                <!-- 用于设置间距 -->
                <el-tab-pane disabled>
                    <template #label>
                        <div style="margin-left: 400px;"></div>
                    </template>
                </el-tab-pane>
                <el-tab-pane name="sceneButton" disabled v-if="activeName == 'relateScene'">
                    <template #label>
                        <el-button type="" plain size="small" @click="getScenesByAlertIds"
                            v-auths="['iot:alert:add']">刷新</el-button>
                        <el-button type="" plain size="small" @click="addAlertScenes"
                            v-auths="['iot:alert:add']">添加场景</el-button>
                    </template>
                </el-tab-pane>
                <el-tab-pane name="notifyButton" disabled v-else>
                    <template #label>
                        <el-button type="" plain size="small" @click="getNotifyTempsByAlertId"
                            v-auths="['iot:alert:add']">刷新</el-button>
                        <el-button type="" plain size="small" @click="addAlertNotifyTemp"
                            v-auths="['iot:alert:add']">添加模板</el-button>
                    </template>
                </el-tab-pane>
            </el-tabs>

            <template #footer>
                <div class="dialog-footer">
                    <!-- <el-button type="primary" @click="handleSubmitForm">确 定</el-button> -->
                    <el-button @click="handleCancel">取 消</el-button>
                    <el-button type="primary" @click="handleSubmitForm(DialogFormRef)" v-auths="['iot:alert:edit']"
                        v-show="dialogData.tableData.ruleForm.alertId">修
                        改</el-button>
                    <el-button type="primary" @click="handleSubmitForm(DialogFormRef)" v-auths="['iot:alert:add']"
                        v-show="!dialogData.tableData.ruleForm.alertId">新
                        增</el-button>

                </div>
            </template>
        </el-dialog>
        <!-- <SceneDialog ref="SceneDialogRef" @refresh="getTableData()" /> -->
        <!-- 选择场景对话框 -->
        <scene-list ref="sceneListRef" @sceneEvent="getSceneData($event)" />
        <!-- 选择通知模板 -->
        <notify-temp-list ref="notifyTempListRef" @notifyEvent="getNotifyTempData($event)" />
    </div>
</template>

<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
import { parseTime } from '/@/utils/next'
import { addAlert, delAlert, getAlert, getScenesByAlertId, listAlert, listNotifyTemplate, updateAlert } from '/@/api/iot/alert';


const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const sceneList = defineAsyncComponent(() => import('/@/views/iot/alert/scene-list.vue'));
const notifyTempList = defineAsyncComponent(() => import('/@/views/iot/alert/notify-temp-list.vue'));
interface Option {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
// 定义变量内容
const sceneListRef = ref();
const notifyTempListRef = ref();
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            alertId: '' as any,
            alertName: '' as any,
            alertLevel: '',

        },
    },
});
const dialogData = reactive({
    tableData: {
        ruleForm: {
            scenes: [] as any[],
            notifyTemplateList: [] as any[],
            status: 2,
            alertName: '',
            alertLevel: '',
            remark: '',
            alertId: '' as any
        },
        dialog: {
            isShowDialog: false,
            title: '',
            sceneLoading: false,
            notifyLoading: false,
        },
    },
})
// 校验规则
const rules = reactive({
    alertName: [
        {
            required: true,
            message: '告警名称不能为空',
            trigger: 'blur',
        },
    ],
    alertLevel: [
        {
            required: true,
            message: '告警级别不能为空',
            trigger: 'change',
        },
    ],

})
// 定义变量内容
const DialogFormRef = ref();
const showSearch = ref(true)    // 显示搜索条件
const ids = ref() //alertId
const alert_level_list = ref<Option[]>([]);
const job_status_list = ref<Option[]>([]);
const channel_type_list = ref<Option[]>([]);
const activeName = ref('relateScene')
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listAlert(state.tableData.param);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;
        // console.log(state.tableData.data);

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        alertId: '' as any,
        alertName: '' as any,
        alertLevel: ''
    }
}
/**获取告警关联场景列表*/
const getScenesByAlertIds = () => {
    if (dialogData.tableData.ruleForm.alertId) {
        dialogData.tableData.dialog.sceneLoading = true;
        try {
            getScenesByAlertId(dialogData.tableData.ruleForm.alertId).then((response) => {
                dialogData.tableData.ruleForm.scenes = response.data.rows;
                dialogData.tableData.dialog.sceneLoading = false;
            });
        } catch (error) {
            console.error('Error fetching table data:', error);
        } finally {
            setTimeout(() => {
                dialogData.tableData.dialog.sceneLoading = false;
            }, 500);
        }
    }
}
/**获取告警通知模板列表*/
const getNotifyTempsByAlertId = () => {
    if (dialogData.tableData.ruleForm.alertId) {
        dialogData.tableData.dialog.notifyLoading = true;
        try {
            listNotifyTemplate(dialogData.tableData.ruleForm.alertId).then((response) => {
                dialogData.tableData.ruleForm.notifyTemplateList = response.rows;
                dialogData.tableData.dialog.notifyLoading = false;
            });
        } catch (error) {
            console.error('Error fetching table data:', error);
        } finally {
            setTimeout(() => {
                dialogData.tableData.dialog.notifyLoading = false;
            }, 500);
        }
    }
}
/**添加场景*/
const addAlertScenes = () => {
    sceneListRef.value.openDialog()
    if (dialogData.tableData.ruleForm.scenes) {
        console.log(dialogData.tableData.ruleForm.scenes);

        let sceneList = JSON.parse(JSON.stringify(dialogData.tableData.ruleForm.scenes));
        sceneListRef.value.selectScenes = sceneList;
        sceneListRef.value.ids = sceneList.map((item: any) => item.sceneId);
    }
}
/**添加通知模板*/
const addAlertNotifyTemp = () => {
    notifyTempListRef.value.openDialog();
    if (dialogData.tableData.ruleForm.notifyTemplateList) {
        let list = JSON.parse(JSON.stringify(dialogData.tableData.ruleForm.notifyTemplateList));
        notifyTempListRef.value.selectNotifyTemps = list;
        notifyTempListRef.value.ids = list.map((item: any) => item.id);
    }
}
/**获取场景数据*/
const getSceneData = (data: any) => {
    dialogData.tableData.ruleForm.scenes = data;
}
/**获取通知模板数据*/
const getNotifyTempData = (data: any) => {
    dialogData.tableData.ruleForm.notifyTemplateList = data;
}
/** 移除告警场景项*/
const handleAlertSceneRemove = (row: any) => {
    for (let i = 0; i < dialogData.tableData.ruleForm.scenes.length; i++) {
        if (row.sceneId == dialogData.tableData.ruleForm.scenes[i].sceneId) {
            dialogData.tableData.ruleForm.scenes.splice(i, 1);
        }
    }
}
/** 移除告警通知项*/
const handleAlertNotifyTempRemove = (row: any) => {
    for (let i = 0; i < dialogData.tableData.ruleForm.notifyTemplateList.length; i++) {
        if (row.id == dialogData.tableData.ruleForm.notifyTemplateList[i].id) {
            dialogData.tableData.ruleForm.notifyTemplateList.splice(i, 1);
        }
    }
}
// 清空弹框数据
const rest = () => {
    dialogData.tableData = {
        ruleForm: {
            scenes: [] as any[],
            notifyTemplateList: [] as any[],
            status: 2,
            alertName: '',
            alertLevel: '',
            remark: '',
            alertId: '' as any
        },
        dialog: {
            isShowDialog: false,
            title: '',
            sceneLoading: false,
            notifyLoading: false,
        },
    }
    activeName.value = 'relateScene'
}
// 打开新增弹窗
const handleAdd = (type: string) => {
    rest()
    dialogData.tableData.dialog.isShowDialog = true
    dialogData.tableData.dialog.title = '添加告警配置'
};
// 打开修改弹窗
const handleUpdate = (row: any | undefined) => {
    rest()
    const alertId = row.alertId || ids.value;
    getAlert(alertId).then((response) => {
        dialogData.tableData.ruleForm = response.data.data;
        dialogData.tableData.dialog.isShowDialog = true
        dialogData.tableData.dialog.title = '修改设备告警'
    });
};
// 删除分类
const handleDelete = (row: any) => {
    const alertIds = row.alertId || ids.value;
    ElMessageBox.confirm(`是否确认删除设备告警编号为${alertIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delAlert(alertIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
// 取消按钮
const handleCancel = () => {
    dialogData.tableData.dialog.isShowDialog = false;
    resetQuery();
}
// 提交
const handleSubmitForm = async (formEl: FormInstance | undefined) => {
    console.log(formEl);

    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (dialogData.tableData.ruleForm.alertId != '') {
                updateAlert(dialogData.tableData.ruleForm).then((res) => {
                    ElMessage.success('修改成功');
                    dialogData.tableData.dialog.isShowDialog = false;
                    getTableData();
                });
            } else {
                addAlert(dialogData.tableData.ruleForm).then((res) => {
                    ElMessage.success('新增成功');
                    dialogData.tableData.dialog.isShowDialog = false;
                    getTableData();
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};
// 获取状态数据
const getdictdata = async () => {
    try {
        alert_level_list.value = await dictStore.fetchDict('iot_alert_level')
        job_status_list.value = await dictStore.fetchDict('sys_job_status')
        channel_type_list.value = await dictStore.fetchDict('notify_channel_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>
