<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-form ref="queryForm" :inline="true" label-width="68px" style="margin-bottom:-20px;">
                <el-form-item label="设备名称" prop="deviceName">
                    <el-input v-model="state.tableData.param.deviceName" placeholder="请输入设备名称" clearable
                        size="default" />
                </el-form-item>
                <el-form-item label="设备编号" prop="serialNumber">
                    <el-input v-model="state.tableData.param.serialNumber" placeholder="请输入设备编号" clearable
                        size="default" />
                </el-form-item>
                <el-form-item label="设备状态" prop="status">
                    <el-select style="width: 240px" v-model="state.tableData.param.status" placeholder="请选择设备状态"
                        clearable size="default">
                        <el-option v-for="dict in device_status_list" :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button size="default" type="primary" class="ml10" @click="handleSearch">
                        <el-icon>
                            <ele-Search />
                        </el-icon>
                        查询
                    </el-button>
                    <el-button size="default" @click="resetQuery">
                        <el-icon><ele-Refresh /></el-icon>
                        重置
                    </el-button>
                </el-form-item>
                <el-form-item style="float:right;">
                    <el-button style="margin-left: 15px;" type="primary" plain size="default"
                        @click="handleChangeShowType"><el-icon><ele-Grid /></el-icon>切换</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card style=" margin-top: 10px" shadow="hover">
            <el-row>
                <el-col :span="4">
                    <el-input v-model="groupName" size="default" placeholder="请输入分类名称" style="max-width: 90%">
                    </el-input>
                    <div class="mt10">
                        <el-tree :data="treeState.tableData.data" :props="defaultProps" :expand-on-click-node="false"
                            :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all
                            highlight-current @node-click="handleNodeClick" />
                    </div>
                </el-col>
                <el-col :span="20" :xs="24">
                    <div v-if="showType == 'list'">
                        <el-table v-loading="state.tableData.loading" :data="deviceList" border style="width: 100%"
                            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                            <el-table-column label="编号" align="center" header-align="center" prop="deviceId" width="100" />
                            <el-table-column label="设备名称" align="center" header-align="center" prop="deviceName"
                                min-width="50">
                                <template #default="scope">
                                    <el-link
                                        v-if="scope.row.status === 3"
                                        :underline="false"
                                        style="font-weight: bold; font-size: 14px; cursor: pointer"
                                        @click.stop="showWellboreInfo(scope.row.deviceId)"
                                    >
                                        <span>{{ scope.row.deviceName }}</span>
                                    </el-link>
                                    <span v-else>{{ scope.row.deviceName }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="设备编号" align="center" prop="serialNumber" width="150" />
                            <el-table-column label="所属产品" align="center" prop="productName" width="120" />
                            <el-table-column label="状态" align="center" prop="status" width="100">
                                <template #default="scope">
                                    <dict-tag :options="device_status_list" :value="scope.row.status" size="small" />
                                </template>
                            </el-table-column>
                            <el-table-column label="激活时间" align="center" prop="activeTime" width="120">
                                <template #default="scope">
                                    <span>{{ parseTime(scope.row.activeTime, '{y}-{m}-{d}') }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="创建时间" align="center" prop="createTime" width="120">
                                <template #default="scope">
                                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                                </template>
                            </el-table-column>
                        </el-table>

                        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
                            class="mt15" style="justify-content: flex-end;" :pager-count="5"
                            :page-sizes="[12, 24, 36, 60]" v-model:current-page="state.tableData.param.pageNum"
                            background v-model:page-size="state.tableData.param.pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total">
                        </el-pagination>
                    </div>
                    <div v-if="showType == 'card'">
                        <el-row :gutter="30" v-loading="state.tableData.loading">
                            <el-col v-for="(item, index) in deviceList" :key="index" :xs="24" :sm="12" :md="12"
                                :lg="8" :xl="6" style="margin-bottom: 30px; text-align: center;">
                                <el-card :body-style="{ padding: '20px' }" shadow="always" class="card-item">
                                    <el-row type="flex" :gutter="10" justify="space-between">
                                        <el-col :span="18" style="text-align: left">
                                        <!-- 在线状态的处理 -->
                                        <el-link
                                            v-if="item.status === 3"
                                            :underline="false"
                                            style="font-weight: bold; font-size: 16px; line-height: 32px; cursor: pointer"
                                            @click.stop="showWellboreInfo(item.deviceId)"
                                        >
                                            <SvgIcon :name="'device'" :type="'menu'" :color="''" />
                                            <span style="margin: 0 5px">{{ item.deviceName }}</span>
                                        </el-link>
                                        <!-- 离线状态的处理 -->
                                        <el-link v-else :underline="false"
                                            style="font-weight: bold; font-size: 16px; line-height: 32px; color: #999;"
                                            :disabled="true"
                                        >
                                            <SvgIcon :name="'device'" :type="'menu'" color="#ccc" />
                                            <span style="margin: 0 5px">{{ item.deviceName }}</span>
                                        </el-link>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="10">
                                        <el-col :span="14">
                                            <div style="text-align: left; line-height: 40px; white-space: nowrap">
                                                <dictTag :options="device_status_list" :value="item.status" size="small"
                                                    style="display: inline-block" />
                                                <span style="display: inline-block; margin: 0 10px">
                                                    <el-tag type="success" class="primary-tag" size="small"
                                                        v-if="item.protocolCode">{{
                                                            item.protocolCode
                                                        }}</el-tag>

                                                </span>
                                                <el-tag type="success" class="primary-tag" size="small"
                                                    v-if="item.transport">{{
                                                        item.transport
                                                    }}</el-tag>
                                            </div>
                                            <el-descriptions :column="1" size="small" style="white-space: nowrap">
                                                <el-descriptions-item label="编号">
                                                    {{ item.serialNumber }}
                                                </el-descriptions-item>
                                                <el-descriptions-item label="产品">
                                                    {{ item.productName }}
                                                </el-descriptions-item>
                                                <el-descriptions-item label="激活时间">
                                                    {{ parseTime(item.activeTime, '{y}-{m}-{d}') }}
                                                </el-descriptions-item>
                                            </el-descriptions>
                                        </el-col>
                                        <el-col :span="10">
                                            <div style="margin-top: 10px">
                                                <el-image style="width: 100%; height: 100px; border-radius: 10px" lazy
                                                    :preview-src-list="[baseUrl + item.imgUrl]"
                                                    :src="baseUrl + item.imgUrl" fit="cover"
                                                    v-if="item.imgUrl != null && item.imgUrl != ''"></el-image>
                                                <el-image style="width: 100%; height: 100px; border-radius: 10px"
                                                    :preview-src-list="[gateway]" :src="gateway" fit="cover"
                                                    v-else-if="item.deviceType == 2"></el-image>
                                                <el-image style="width: 100%; height: 100px; border-radius: 10px"
                                                    :preview-src-list="[video]" :src="video" fit="cover"
                                                    v-else-if="item.deviceType == 3"></el-image>
                                                <el-image style="width: 100%; height: 100px; border-radius: 10px"
                                                    :preview-src-list="[product]" :src="product" fit="cover"
                                                    v-else></el-image>
                                            </div>
                                        </el-col>
                                    </el-row>
                                </el-card>
                            </el-col>
                        </el-row>
                        
                        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
                            class="mt15" style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                            v-model:current-page="state.tableData.param.pageNum" background
                            v-model:page-size="state.tableData.param.pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total">
                        </el-pagination>
                    </div>
                </el-col>
            </el-row>
            
            <!-- 井筒信息对话框 -->
            <el-drawer
                v-for="item in onlineDevices"
                :key="item.deviceId"
                v-model="popoverVisibleMap[item.deviceId]"
                direction="rtl"
                size="500px"
                :with-header="false"
                :modal="true"
                :append-to-body="true"
                :destroy-on-close="true"
                :show-close="false"
                :close-on-click-modal="true"
                custom-class="wellbore-drawer"
                @close="closeWellboreInfo(item.deviceId)"
            >
                <WellboreInfo
                    :deviceId="item.deviceId"
                    @close="closeWellboreInfo(item.deviceId)"
                />
            </el-drawer>
        </el-card>
    </div>
</template>
<script setup lang="ts" name="">
import { reactive, onMounted, ref, onActivated, watch, nextTick, computed ,defineAsyncComponent} from 'vue';
import { ElTree } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';
import { listGroup } from '/@/api/iot/group';
import { useUserInfo } from '/@/stores/userInfo';
import { useRoute, useRouter } from 'vue-router';
import { handleTree, parseTime } from '/@/utils/next'
import { listDeviceShort } from '/@/api/iot/device';
import gateway from '/@/assets/images/gateway.png';
import video from '/@/assets/images/video.png';
import product from '/@/assets/images/product.png';
import { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type';
// 引入组件
const WellboreInfo = defineAsyncComponent(() => import('/@/views/iot/wellbore/wellbore-info.vue'));


// 控制弹出框的显示和隐藏
const popoverVisibleMap = ref<Record<number, boolean>>({});

// 显示井筒信息
const showWellboreInfo = (deviceId: number) => {
  // 关闭其他弹出框，只保留当前点击的
  closeAllPopoverExcept(deviceId);
  
  // 使用nextTick确保DOM更新后再打开新的页面
  nextTick(() => {
    // 打开当前点击的井筒信息页面
    popoverVisibleMap.value[deviceId] = true;
  });
};

// 关闭井筒信息
const closeWellboreInfo = (deviceId: number) => {
  popoverVisibleMap.value[deviceId] = false;
};

// 关闭除了指定设备外的所有弹出框
const closeAllPopoverExcept = (exceptDeviceId: number) => {
  if (deviceList.value && deviceList.value.length > 0) {
    deviceList.value.forEach(device => {
      if (device.deviceId !== exceptDeviceId) {
        popoverVisibleMap.value[device.deviceId] = false;
      }
    });
  }
  
  console.log('关闭其他弹出框，保留设备ID:', exceptDeviceId);
};

// 类型定义
interface ProductTypeOption {
    productName: string;
    isOwner: boolean;
    protocolCode: string;
    deviceName: string;
    rssi: string;
    productId: number;
    transport: string;
    imgUrl: string;
    status: string;
    deviceType: number;
    serialNumber: string;
    activeTime: string;
    isShadow: boolean;
    deviceId: number;
    subDeviceCount: number;
}

interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}

interface GroupOption {
    groupId: string;
    groupName: string;
}

interface QueryParams {
    pageNum: number;
    pageSize: number;
    deviceName?: string;
    serialNumber?: string;
    status: string;
    userId?: string | number;
    productId: number | null;
    groupId: number | null;
}

interface StateType {
    tableData: {
        data: any[];
        total: number;
        loading: boolean;
        param: QueryParams;
    };
}

interface TreeStateType {
    tableData: {
        data: any[];
        loading: boolean;
        param: {
            groupName?: string;
            groupId: number;
            userId?: string | number;
        };
    };
}

// 组件实例
const route = useRoute();
const router = useRouter();
const userInfoStore = useUserInfo();
const dictStore = useDictStore();
const tree = ref<InstanceType<typeof ElTree> | null>(null);

// 基础数据
const showType = ref('card');
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API);
const groupName = ref<string>();

// 状态管理
const state = reactive<StateType>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            deviceName: undefined,
            serialNumber: undefined,
            status: '',
            userId: undefined,
            productId: 91, // 默认值
            groupId: null,
        },
    },
});

const treeState = reactive<TreeStateType>({
    tableData: {
        data: [],
        loading: false,
        param: {
            groupName: undefined,
            groupId: 21, // 默认值
            userId: undefined
        },
    },
});

// 数据列表
const deviceList = ref<ProductTypeOption[]>([]);
const device_status_list = ref<TypeOption[]>([]);
const myGroupList = ref<GroupOption[]>([]);
const location_way_list = ref<TypeOption[]>([]);
const methodlist = ref<TypeOption[]>([]);
const chiplist = ref<TypeOption[]>([]);

// 树形配置
const defaultProps = reactive({
    children: "children",
    label: "groupName"
});

// 筛选节点
const filterNode = (value: string, data: TreeNodeData) => {
    if (!value) return true;
    return data.groupName.indexOf(value) !== -1;
}

// 工具函数
const updateQueryParams = (params: Partial<QueryParams>) => {
    state.tableData.param = {
        ...state.tableData.param,
        ...params
    };
};

const getQueryParamsFromRoute = () => {
    return {
        pageNum: route.query.pageNum ? Number(route.query.pageNum) : 1,
        pageSize: route.query.pageSize ? Number(route.query.pageSize) : 10,
        deviceName: route.query.deviceName as string,
        serialNumber: route.query.serialNumber as string,
        status: route.query.status as string,
        groupId: route.query.groupId ? Number(route.query.groupId) : null
    };
};

// 页面操作函数
const handleChangeShowType = () => {
    showType.value = showType.value === 'card' ? 'list' : 'card';
    updateQueryParams({ pageNum: 1 });
    getTableData();
    reloadPageWithDeviceId();
};

const handleSearch = async () => {
    updateQueryParams({ pageNum: 1 });
    await getTableData();
    reloadPageWithDeviceId();
};

const handleNodeClick = async (data: { groupId: any; }) => {
    updateQueryParams({
        groupId: data.groupId,
        pageNum: 1
    });
    await getTableData();
    reloadPageWithDeviceId();
};

const resetQuery = async () => {
    updateQueryParams({
        pageNum: 1,
        pageSize: 10,
        deviceName: undefined,
        serialNumber: undefined,
        status: '',
        userId: undefined,
        productId: null,
        groupId: null
    });
    await getTableData();
    reloadPageWithDeviceId();
};

// 分页处理
const onHandleSizeChange = async (val: number) => {
    updateQueryParams({
        pageSize: val,
        pageNum: 1
    });
    await getTableData();
    reloadPageWithDeviceId();
};

const onHandleCurrentChange = async (val: number) => {
    updateQueryParams({ pageNum: val });
    await getTableData();
    reloadPageWithDeviceId();
};

// 数据加载函数
const getTableData = async () => {
    try {
        state.tableData.loading = true;
        const response = await listDeviceShort(state.tableData.param);
        state.tableData.total = response.data.total;
        deviceList.value = response.data.rows;
        
        // 数据加载完成后初始化弹出框状态
        nextTick(() => {
            resetAllPopoverState();
        });
    } catch (error) {
        console.error('加载设备数据失败:', error);
    } finally {
        state.tableData.loading = false;
    }
};

const getTreeData = async () => {
    try {
        const response = await listGroup(treeState.tableData.param);
        treeState.tableData.data = handleTree(response.data.data, "groupId");
    } catch (error) {
        console.error('Error fetching tree data:', error);
    }
};

const getDictData = async () => {
    try {
        const [deviceStatus, locationWay, networkMethod, deviceChip] = await Promise.all([
            dictStore.fetchDict('iot_device_status'),
            dictStore.fetchDict('iot_location_way'),
            dictStore.fetchDict('iot_network_method'),
            dictStore.fetchDict('iot_device_chip')
        ]);

        device_status_list.value = deviceStatus;
        location_way_list.value = locationWay;
        methodlist.value = networkMethod;
        chiplist.value = deviceChip;

        const response = await listGroup({
            pageSize: 10,
            pageNum: 1,
            userId: userInfoStore.userInfos.userId,
        });
        myGroupList.value = response.data.rows;
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};

// 页面路由处理
const reloadPageWithDeviceId = () => {
    const { pageNum, pageSize, deviceName, serialNumber, status, groupId } = state.tableData.param;
    router.push({
        query: {
            ...route.query,
            pageNum,
            pageSize,
            deviceName: deviceName || undefined,
            serialNumber: serialNumber || undefined,
            status: status || undefined,
            groupId: groupId || undefined,
            showType: showType.value,
            t: new Date().getTime()
        }
    });
};

// 监听器
watch(groupName, (val) => {
  if (tree.value) {
    tree.value.filter(val);
  }
});

// 确保在页面刷新时所有弹出框都是关闭状态
const resetAllPopoverState = () => {
  console.log('重置所有弹出框状态');
  
  // 初始化弹出框状态对象
  if (!popoverVisibleMap.value) {
    popoverVisibleMap.value = {};
  }
  
  // 如果设备列表已加载，则确保所有设备的弹出框状态都是关闭的
  if (deviceList.value && deviceList.value.length > 0) {
    deviceList.value.forEach(device => {
      popoverVisibleMap.value[device.deviceId] = false;
    });
  }
};

// 生命周期钩子
onMounted(() => {
  updateQueryParams(getQueryParamsFromRoute());
  getTableData();
  getDictData();
  getTreeData();
  const viewType = route.query.showType as string;
  if (viewType) {
    showType.value = viewType;
  }
  // 确保所有弹出框都是关闭状态
  resetAllPopoverState();
});

onActivated(() => {
  if (route.query.t) {
    updateQueryParams(getQueryParamsFromRoute());
    getTableData();
    getDictData();
    // 确保所有弹出框都是关闭状态
    resetAllPopoverState();
  }
});

// 计算属性：过滤出在线设备
const onlineDevices = computed(() => {
  if (!deviceList.value || deviceList.value.length === 0) {
    console.log('设备列表为空');
    return [];
  }
  
  // 输出设备状态信息
  const statusCounts = {};
  deviceList.value.forEach(item => {
    const status = item.status;
    statusCounts[status] = (statusCounts[status] || 0) + 1;
  });
  console.log('设备状态统计:', statusCounts);
  
  const filtered = deviceList.value.filter(item => {
    // 处理状态可能是数字或字符串的情况
    return item.status === '3' || item.status === 3;
  });
  
  console.log('在线设备数量:', filtered.length);
  return filtered;
});
</script>
<style scoped>
.card-item {
    border-radius: 15px;
}

.primary-tag {
    background-color: #ebf5ff;
    /* 设置类似 primary 的蓝色背景 */
    color: #1890ff;
}

:deep(.el-descriptions__cell) {
    display: flex !important;
}

:deep(.wellbore-drawer) {
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
    background-color: transparent !important;
}

:deep(.wellbore-drawer .el-drawer__body) {
    padding: 0;
    border: none;
    background-color: transparent;
}

:deep(.wellbore-drawer .el-drawer__container) {
    outline: none;
}

:deep(.wellbore-drawer .el-overlay) {
    background-color: rgba(0, 0, 0, 0.5);
}

:deep(.wellbore-drawer .el-drawer) {
    background-color: transparent !important;
    box-shadow: none !important;
}

.drawer-custom-header {
    background-color: #f9f9f9;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
}

.drawer-title {
    font-weight: bold;
    font-size: 16px;
    color: #333;
}

.close-button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background-color: #f56c6c;
    cursor: pointer;
    transition: all 0.2s;
}

.close-button:hover {
    background-color: #ff7875;
}

.icon-close {
    color: #ffffff;
    font-size: 16px;
}
</style>