<template>
  <div class="wellbore-info" v-loading="loading">
    <div class="well-header" v-if="!loading && hasData">
      <!-- 标题栏 -->
      <div class="well-title">
        {{ wellboreData.wellName }}
        <div class="status-indicators">
          <el-icon class="icon-online"><SuccessFilled /></el-icon>
          <el-icon class="icon-warning" v-if="false"><Warning /></el-icon>
          <el-icon class="icon-danger" v-if="false"><CircleCloseFilled /></el-icon>
          <div class="close-button" @click="closeWellbore">
            <el-icon class="icon-close"><Close /></el-icon>
          </div>
        </div>
      </div>

      <!-- 主要参数区域 -->
      <div class="main-params">
        <div class="param-row">
          <div class="param-item">
            <span class="label">井名:</span>
            <span class="value">{{ wellboreData.wellName }}</span>
          </div>
        </div>
        <div class="param-row">
          <div class="param-item">
            <span class="label">油压:</span>
            <span class="value">{{ getValue('WIP') }}{{ getValueUnit('WIP') }}</span>
          </div>
          <div class="param-item">
            <span class="label">套压:</span>
            <span class="value">{{ getValue('CPV') }}{{ getValueUnit('CPV') }}</span>
          </div>
        </div>
        <div class="param-row">
          <div class="param-item">
            <span class="label">井口温度:</span>
            <span class="value">{{ getValue('TWT') }}{{ getValueUnit('TWT') }}</span>
          </div>
        </div>
        <div class="param-row">
          <div class="param-item">
            <span class="label">冲程:</span>
            <span class="value">{{ getValue('SLV') }}{{ getValueUnit('SLV') }}</span>
          </div>
          <div class="param-item">
            <span class="label">冲次:</span>
            <span class="value">{{ getValue('CHC') }}{{ getValueUnit('CHC') }}</span>
          </div>
        </div>
        <div class="param-row">
          <div class="param-item">
            <span class="label">有功功率:</span>
            <span class="value">{{ getValue('ZYG') }}{{ getValueUnit('ZYG') }}</span>
          </div>
          <div class="param-item">
            <span class="label">无功功率:</span>
            <span class="value">{{ getValue('ZWG') }}{{ getValueUnit('ZWG') }}</span>
          </div>
        </div>
        <div class="param-row">
          <div class="param-item">
            <span class="label">功率因数:</span>
            <span class="value">{{ getValue('GYS') }}{{ getValueUnit('GYS') }}</span>
          </div>
          <div class="param-item">
            <span class="label">有功用电量:</span>
            <span class="value">{{ getValue('YGL') }}{{ getValueUnit('YGL') }}</span>
          </div>
        </div>
      </div>

      <!-- 电气参数区域 -->
      <div class="section-block">
        <div class="section-header">
          <span class="section-title">电气参数</span>
          <el-icon class="collapse-icon"><ArrowUp /></el-icon>
        </div>
        <div class="section-content">
          <div class="electrical-params">
            <div class="param-row">
              <div class="param-col">
                <span class="label">A相电压:</span>
                <span class="value">{{ getValue('ADY') }}{{ getValueUnit('ADY') }}</span>
              </div>
              <div class="param-col">
                <span class="label">A相电流:</span>
                <span class="value">{{ getValue('ADL') }}{{ getValueUnit('ADL') }}</span>
              </div>
            </div>
            <div class="param-row">
              <div class="param-col">
                <span class="label">B相电压:</span>
                <span class="value">{{ getValue('BDY') }}{{ getValueUnit('BDY') }}</span>
              </div>
              <div class="param-col">
                <span class="label">B相电流:</span>
                <span class="value">{{ getValue('BDL') }}{{ getValueUnit('BDL') }}</span>
              </div>
            </div>
            <div class="param-row">
              <div class="param-col">
                <span class="label">C相电压:</span>
                <span class="value">{{ getValue('CDY') }}{{ getValueUnit('CDY') }}</span>
              </div>
              <div class="param-col">
                <span class="label">C相电流:</span>
                <span class="value">{{ getValue('CDL') }}{{ getValueUnit('CDL') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功图分析区域 -->
      <div class="section-block">
        <div class="section-header">
          <span class="section-title">功图分析</span>
          <el-icon class="collapse-icon"><ArrowUp /></el-icon>
        </div>
        <div class="section-content">
          <div class="diagram-tabs">
            <el-tabs v-model="activeChartTab" class="chart-tabs" type="card">
              <el-tab-pane label="地面工图" name="groundDiagram"></el-tab-pane>
              <el-tab-pane label="电工图" name="electricalDiagram"></el-tab-pane>
            </el-tabs>
          </div>
          <div class="chart-info">
            <div class="chart-values">
              <span>采集时间: {{ wellboreData.checkDate }}</span>
            </div>
          </div>
          <div class="chart-container">
            <div class="chart-wrapper">
              <div id="wellboreChart" ref="chartRef" class="chart"></div>
            </div>
            <div class="chart-info">
              <div class="chart-values">
                <div v-if="activeChartTab === 'groundDiagram'">
                  <span class="chart-value-item">最大载荷: {{ getMaxDispLoad() }} kN</span>
                  <span class="chart-value-item">最小载荷: {{ getMinDispLoad() }} kN</span>
                </div>
                <div v-else-if="activeChartTab === 'electricalDiagram'">
                  <span class="chart-value-item">最大电流: {{ getMaxCurrentValue() }}A</span>
                  <span class="chart-value-item">最小电流: {{ getMinCurrentValue() }}A</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空数据状态展示 -->
    <el-empty 
      v-if="!loading && !hasData" 
      description="暂无数据"
      :image-size="200"
    >
      <template #description>
        <p>暂无井筒数据信息</p>
      </template>
      <el-button type="primary" @click="getData">重新加载</el-button>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, nextTick, watch, onUnmounted, computed } from 'vue'
import { listWellbore } from '/@/api/iot/device'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { Warning, Close, ArrowUp, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'

const props = defineProps({
  deviceId: {
    type: [Number, String],
    required: true
  }
})

const emit = defineEmits(['close'])

// 关闭井筒详情页面
const closeWellbore = () => {
  emit('close')
}

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null
let resizeHandler: (() => void) | null = null  // 添加resizeHandler引用
let resizeObserver: ResizeObserver | null = null;
const activeChartTab = ref('groundDiagram') // 默认显示地面工图

const wellboreData = reactive({
  // 基础信息
  station: '',        // 所属场站
  wellName: '',       // 井名
  status: '',         // 通讯状态
  checkDate: '',      // 功图采集时间
  dynaPoints: [] as number[],  // 功图点数
  // 功图数据
  dispLoad: '',       // 载荷
  dispCurrent: '',    // 电流
  displacement: '',   // 位移
  // 实时数据
  values: [] as Array<{
    id: string;
    value: number | string;
  }>,
})

const loading = ref(false)
const hasData = ref(false)

// 数据标签映射
const labelMap = {
  TWT: '井口温度',
  WIP: '井口压力',
  CPV: '套压',
  SLV: '冲程',
  CHC: '冲次',
  UWL: '最大载荷',
  DWL: '最小载荷',
  WBR: '平衡度',
  ADL: 'A相电流',
  BDL: 'B相电流',
  CDL: 'C相电流',
  ADY: 'A相电压',
  BDY: 'B相电压',
  CDY: 'C相电压',
  GYS: '功率因数',
  ZYG: '总有功功率',
  ZWG: '总无功功率',
  YGL: '有功用电量'
}

// 单位映射
const unitMap = {
  TWT: '℃',
  WIP: 'Mpa',
  CPV: 'Mpa',
  SLV: 'm',
  CHC: '次/分',
  UWL: 'KN',
  DWL: 'KN',
  WBR: '%',
  ADL: 'A',
  BDL: 'A',
  CDL: 'A',
  ADY: 'V',
  BDY: 'V',
  CDY: 'V',
  GYS: '',
  ZYG: 'KW',
  ZWG: 'KVar',
  YGL: 'KWh'
}

// 获取数据标签
const getValueLabel = (id: string) => {
  return labelMap[id] || id
}

// 获取单位
const getValueUnit = (id: string) => {
  return unitMap[id] || ''
}

// 获取指定参数值
const getValue = (id: string) => {
  const item = wellboreData.values.find(v => v.id === id)
  return item ? item.value : ''
}

// 计算属性：解析位移数据
const displacementData = computed(() => {
  return wellboreData.displacement?.split('|').map(Number) || []
})

// 计算属性：解析载荷数据
const dispLoadData = computed(() => {
  return wellboreData.dispLoad?.split('|').map(Number) || []
})

// 计算属性：解析电流数据
const dispCurrentData = computed(() => {
  return wellboreData.dispCurrent?.split('|').map(Number) || []
})

// 计算载荷的最大值
const getMaxDispLoad = () => {
  return dispLoadData.value.length ? Math.max(...dispLoadData.value).toFixed(2) : '0'
}

// 计算载荷的最小值
const getMinDispLoad = () => {
  return dispLoadData.value.length ? Math.min(...dispLoadData.value).toFixed(2) : '0'
}
  
// 计算电流最大值
const getMaxCurrentValue = () => {
  return dispCurrentData.value.length ? Math.max(...dispCurrentData.value).toFixed(2) : '0'
}

// 计算电流最小值
const getMinCurrentValue = () => {
  return dispCurrentData.value.length ? Math.min(...dispCurrentData.value).toFixed(2) : '0'
}

// 计算属性：获取当前图表的配置
const chartOption = computed(() => {
  if (!displacementData.value.length) return null;
  
  // 根据当前选择的图表类型，选择不同的Y轴数据
  let yData = activeChartTab.value === 'groundDiagram' ? dispLoadData.value : dispCurrentData.value;
  let yAxisName = activeChartTab.value === 'groundDiagram' ? '载荷(kN)' : '电流(A)';
  let seriesNames = activeChartTab.value === 'groundDiagram' ? ['上行程', '下行程'] : ['上行程电流', '下行程电流'];
  let seriesColors = activeChartTab.value === 'groundDiagram' ? ['#1890ff', '#f5222d'] : ['#52c41a', '#fa8c16'];
  
  if (!yData.length) return null;
  
  // 找到位移数据的最大值位置
  const maxDisplacementIndex = displacementData.value.indexOf(Math.max(...displacementData.value));
  
  // 分割上下行程数据 - 根据最大位移点分割
  const upstrokeDisp = displacementData.value.slice(0, maxDisplacementIndex + 1);  // 从开始到最高点为上行程
  const upstrokeY = yData.slice(0, maxDisplacementIndex + 1);
  const downstrokeDisp = displacementData.value.slice(maxDisplacementIndex);  // 从最高点到结束为下行程
  const downstrokeY = yData.slice(maxDisplacementIndex);
  
  // 计算X轴的最大值和合适的刻度间隔
  const maxDisplacement = Math.max(...displacementData.value);
  // 先计算一个临时的最大值用于确定间隔
  const tempMaxX = Math.ceil(maxDisplacement * 2) / 2;
  // 根据数据范围计算合适的刻度间隔
  const xInterval = tempMaxX > 5 ? 1 : 0.5;
  // 向上取整到最接近的刻度间隔的整数倍
  const maxX = Math.ceil(maxDisplacement / xInterval) * xInterval;
  
  // 定义tooltip格式化函数
  const tooltipFormatter = (params) => {
    const type = params[0].seriesName;
    const unit = activeChartTab.value === 'groundDiagram' ? 'kN' : 'A';
    const label = activeChartTab.value === 'groundDiagram' ? '载荷' : '电流';
    return `${type}<br/>位移: ${params[0].data[0].toFixed(2)}m<br/>${label}: ${params[0].data[1].toFixed(2)}${unit}`;
  };
  
  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: tooltipFormatter,
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      }
    },
    legend: {
      data: seriesNames,
      top: 5,
      left: 'center',
      itemWidth: 20,
      itemHeight: 2,
      textStyle: {
        color: '#333',
        fontSize: 10
      }
    },
    grid: {
      top: 30,
      right: 30,
      bottom: 40,
      left: 50,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '位移(m)',
      nameLocation: 'center',
      nameGap: 25,
      min: 0,
      max: maxX,
      interval: xInterval,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#999',
          width: 1
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#eee',
          type: 'dashed',
          width: 1
        }
      },
      axisLabel: {
        show: true,
        fontSize: 11,
        color: '#666',
        margin: 8,
        formatter: '{value}'
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#999'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: yAxisName,
      nameLocation: 'middle',
      nameGap: 30,
      min: function(value) {
        return Math.floor(value.min * 0.9);
      },
      max: function(value) {
        return Math.ceil(value.max * 1.1);
      },
      splitNumber: 8,  // 增加Y轴分割段数，使刻度更密集
      minInterval: 0.05, // 减小最小间隔，允许更密集的刻度
      axisLine: {
        show: true,
        lineStyle: {
          color: '#999',
          width: 1
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#eee',
          type: 'dashed',
          width: 1
        }
      },
      axisLabel: {
        show: true,
        fontSize: 11,
        color: '#666',
        margin: 8,
        formatter: function(value) {
          return value.toFixed(1);
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#999'
        }
      }
    },
    series: [
      {
        name: seriesNames[0],
        type: 'line',
        data: upstrokeDisp.map((x, index) => [x, upstrokeY[index]]),
        smooth: true,
        lineStyle: {
          width: 2.5,
          color: seriesColors[0]
        },
        symbol: 'circle',
        symbolSize: 4,
        emphasis: {
          lineStyle: {
            width: 3.5
          },
          itemStyle: {
            symbolSize: 7
          }
        }
      },
      {
        name: seriesNames[1],
        type: 'line',
        data: downstrokeDisp.map((x, index) => [x, downstrokeY[index]]),
        smooth: true,
        lineStyle: {
          width: 2.5,
          color: seriesColors[1]
        },
        symbol: 'circle',
        symbolSize: 4,
        emphasis: {
          lineStyle: {
            width: 3.5
          },
          itemStyle: {
            symbolSize: 7
          }
        }
      }
    ]
  };
});

// 初始化图表
const initChart = () => {
  // 尝试获取图表容器
  const chartContainer = chartRef.value || document.getElementById('wellboreChart')
  
  if (!chartContainer) {
    console.warn('图表容器未找到')
    return
  }

  // 确保容器有明确的尺寸
  if (chartContainer.clientWidth === 0 || chartContainer.clientHeight === 0) {
    // 强制设置容器及其所有父元素的样式
    let currentElement: HTMLElement = chartContainer;
    while (currentElement && currentElement !== document.body) {
      if (getComputedStyle(currentElement).display === 'none') {
        currentElement.style.display = 'block';
      }
      if (currentElement.classList.contains('chart')) {
        currentElement.style.width = '100%';
        currentElement.style.height = '300px';
      }
      if (currentElement.classList.contains('chart-container')) {
        currentElement.style.width = '100%';
        currentElement.style.minHeight = '350px';
      }
      // 确保父元素不为null
      if (currentElement.parentElement) {
        currentElement = currentElement.parentElement;
      } else {
        break;
      }
    }
    
    // 使用 requestAnimationFrame 在下一帧尝试初始化
    requestAnimationFrame(() => {
      if (chartContainer.clientWidth > 0 && chartContainer.clientHeight > 0) {
        initChart();
      } else {
        setTimeout(() => initChart(), 500);
      }
    });
    return;
  }

  // 销毁旧的图表实例
  if (chart) {
    chart.dispose()
  }
  
  // 创建新的图表实例
  try {
    chart = echarts.init(chartContainer)
    
    // 检查是否有图表配置
    if (!chartOption.value) {
      ElMessage.warning('图表数据不完整')
      return
    }
    
    // 设置图表配置
    chart.setOption(chartOption.value)
    
    // 优化resize监听
    if (resizeHandler) {
      window.removeEventListener('resize', resizeHandler)
    }
    resizeHandler = () => {
      chart?.resize()
    }
    window.addEventListener('resize', resizeHandler, { passive: true })
    
  } catch (error) {
    console.error('图表初始化失败:', error)
    ElMessage.error('图表渲染失败')
  }
}

// 组件卸载时的清理工作
onUnmounted(() => {
  if (resizeHandler) {
    window.removeEventListener('resize', resizeHandler)
    resizeHandler = null
  }
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
  if (chart) {
    chart.dispose()
    chart = null
  }
})

// 获取数据
const getData = async () => {
  loading.value = true
  hasData.value = false
  
  try {
    const res = await listWellbore(props.deviceId)
    
    if (res?.data?.data) {
      const data = res.data.data
      
      // 更新基础数据
      Object.assign(wellboreData, {
        station: data.station,
        wellName: data.wellName,
        status: data.status,
        checkDate: data.checkDate,
        dynaPoints: data.dynaPoints,
        values: data.values,
        dispLoad: data.dispLoad,
        dispCurrent: data.dispCurrent,
        displacement: data.displacement
      })
      
      hasData.value = true
      
      // 确保DOM更新后再初始化图表
      await nextTick()
      
      // 清理之前的观察者
      if (resizeObserver) {
        resizeObserver.disconnect()
        resizeObserver = null
      }
      
      // 使用ResizeObserver监听容器尺寸变化
      const initChartWhenReady = () => {
        const chartContainer = chartRef.value || document.getElementById('wellboreChart')
        if (!chartContainer) {
          setTimeout(initChartWhenReady, 300)
          return
        }
        
        // 如果容器已有尺寸，直接初始化
        if (chartContainer.clientWidth > 0 && chartContainer.clientHeight > 0) {
          initChart()
          return
        }
        
        // 否则，使用ResizeObserver监听尺寸变化
        resizeObserver = new ResizeObserver((entries) => {
          for (const entry of entries) {
            if (entry.contentRect.width > 0 && entry.contentRect.height > 0) {
              initChart()
              // 任务完成后取消监听
              resizeObserver?.disconnect()
              resizeObserver = null
              break
            }
          }
        })
        
        resizeObserver.observe(chartContainer)
        
        // 设置一个超时，如果在一定时间内没有检测到尺寸变化，强制初始化
        setTimeout(() => {
          if (resizeObserver) {
            resizeObserver.disconnect()
            resizeObserver = null
            initChart()
          }
        }, 3000)
      }
      
      // 开始初始化流程
      initChartWhenReady()
    } else {
      ElMessage.warning('未获取到数据')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('数据加载失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听设备ID变化
watch(() => props.deviceId, (newVal) => {
  if (newVal) {
    getData()
  }
})

// 监听图表类型变化
watch(() => activeChartTab.value, () => {
  if (chart && chartOption.value) {
    nextTick(() => {
      chart.setOption(chartOption.value)
    })
  }
})

// 组件挂载时获取数据
onMounted(() => {
  getData()
})
</script>

<style scoped>
.wellbore-info {
  min-height: 300px;
  position: relative;
  background-color: #f0f2f5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.well-header {
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.well-title {
  padding: 12px 16px;
  background: linear-gradient(to right, #1890ff, #096dd9);
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-indicators {
  display: flex;
  gap: 8px;
  align-items: center;
}

.icon-online {
  color: #67c23a;
  font-size: 16px;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
}

.icon-warning {
  color: #e6a23c;
  font-size: 16px;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
}

.icon-danger {
  color: #f56c6c;
  font-size: 16px;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
}

.close-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  margin-left: 8px;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.icon-close {
  color: #ffffff;
  font-size: 16px;
}

/* 主要参数区域 */
.main-params {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: linear-gradient(to bottom, #f9f9f9, #fff);
}

.param-row {
  display: flex;
  margin-bottom: 10px;
}

.param-row:last-child {
  margin-bottom: 0;
}

.param-item {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
  padding-right: 10px;
}

.label {
  color: #666;
  font-size: 13px;
  white-space: nowrap;
}

.value {
  color: #333;
  font-weight: 500;
  font-size: 13px;
  margin-left: 5px;
}

/* 分区块样式 */
.section-block {
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 1px;
}

.section-header {
  padding: 10px 16px;
  background: linear-gradient(to right, #f5f7fa, #f0f2f5);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
}

.section-header:hover {
  background: linear-gradient(to right, #f0f2f5, #e6f7ff);
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

.collapse-icon {
  font-size: 14px;
  color: #909399;
  transition: transform 0.3s;
}

.section-content {
  padding: 16px;
  background-color: #fff;
}

/* 电气参数区域 */
.electrical-params {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.param-col {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  transition: all 0.3s;
}

.param-col:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* 功图区域 */
.diagram-tabs {
  margin-bottom: 12px;
}

.chart-tabs :deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

.chart-tabs :deep(.el-tabs__active-bar) {
  height: 2px;
}

.chart-tabs :deep(.el-tabs__item) {
  font-size: 14px;
  height: 36px;
  line-height: 36px;
  transition: all 0.3s;
}

.chart-tabs :deep(.el-tabs__item.is-active) {
  color: #1890ff;
  font-weight: 500;
}

.chart-tabs :deep(.el-tabs__item:hover) {
  color: #40a9ff;
}

.chart-tabs :deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid #e8e8e8;
}

.chart-tabs :deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background-color: #e6f7ff;
  border-bottom-color: #e6f7ff;
}

.chart-wrapper {
  width: 100%;
  height: 320px;
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.chart {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.chart-container {
  background: #fff;
  border-radius: 4px;
  padding: 0;
  width: 100%;
}

.chart-info {
  margin-top: 12px;
  background: transparent;
  border-radius: 0;
  padding: 8px 0;
  font-size: 12px;
  border-left: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-meta {
  color: #666;
  margin-bottom: 0;
}

.chart-values {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-value-item {
  color: #333;
  font-weight: 500;
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .param-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .param-item {
    width: 100%;
  }
}
</style>

