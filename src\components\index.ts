import type { Component } from 'vue'
import RightToolbar from '/@/components/RightToolbar/index.vue'
import dictTag from '/@/components/DictTag/index.vue'
import crontab from '/@/components/Crontab/index.vue'
// import editor from '/@/components/DictTag/index.vue'

// ✨如果使用的是 JS 可以删除类型校验s
const globalComponent: {
  [propName: string]: Component
} = {
  RightToolbar,
  dictTag,
  crontab,
}
export default globalComponent
