<template>
	<div id="dom" class="chart-scrollbar">
		<div class="chart-warp">
			<div class="chart-warp-top">
				<ChartHead @child-button-click="toggleFullScreen" @showActive="getShowActive" />
			</div>
			<!-- 运维看板 -->
			<div class="chart-warp-bottom" v-if="activeTab == 0">
				<!-- 左边 -->
				<div class="big-data-down-left">
					<!-- 左1 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">设备监测范围</div>
							<div class="instrumentList" v-show="chartleftOneDatas.length > 0">
								<div class="clearfix" v-for="(item, index) in chartleftOneDatas" :key="item.productId">
									<div class="li">
										<div class="yq" id="yq">{{ item.deviceCount }}</div>
										<span>{{ item.productName }}</span>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- 左2 -->
					<div class="flex-warp-item">
						<!-- <div class="flex-warp-item-box">
							<div class="flex-title">当前设备状态</div>
							<div class="flex-content flex-content-overflow">
								<div class="d-states">
									<div class="d-states-item">
										<SvgIcon name="ele-Odometer" class="i-bg1" />
										<div class="d-states-flex">
											<div class="d-states-item-label">园区设备数</div>
											<div class="d-states-item-value">99</div>
										</div>
									</div>
									<div class="d-states-item">
										<SvgIcon name="ele-FirstAidKit" class="i-bg2" />
										<div class="d-states-flex">
											<div class="d-states-item-label">预警设备数</div>
											<div class="d-states-item-value">10</div>
										</div>
									</div>
									<div class="d-states-item">
										<SvgIcon name="ele-VideoPlay" class="i-bg3" />
										<div class="d-states-flex">
											<div class="d-states-item-label">运行设备数</div>
											<div class="d-states-item-value">20</div>
										</div>
									</div>
								</div>
								<div class="d-btn">
									<div class="d-btn-item" v-for="(v, k) in state.dBtnList" :key="k">
										<i class="d-btn-item-left el-icon-money"></i>
										<div class="d-btn-item-center">
											<div>{{ v.v2 }}|{{ v.v3 }}</div>
										</div>
										<div class="d-btn-item-eight">{{ v.v4 }}</div>
									</div>
								</div>
							</div>
						</div> -->
						<div class="flex-warp-item-box border">
							<div class="flex-title">设备监测动态</div>
							<div class="flex-content">
								<div style="height: 100%;width: 100%;" ref="chartsLeftTwoRef"></div>
							</div>
						</div>
					</div>
					<!-- 左3 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">设备故障分类对比</div>
							<div class="flex-content">
								<div style="height: 100%;" ref="chartsLeftThreeRef"></div>
							</div>
						</div>
					</div>
					<!-- 左4 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">采集齐全率分析</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsLeftFourRef"></div>
							</div>
						</div>
					</div>
				</div>

				<!-- 中间 -->
				<div class="big-data-down-center">
					<div class="big-data-down-center-one">
						<div class="big-data-down-center-one-content border">
							<ThreeScene />
							<!-- <div style="height: 100%" ref="chartsCenterOneRef"></div> -->
						</div>
					</div>
					<div class="big-data-down-center-two">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备故障清单</span>
							</div>
							<div class="flex-content">
								<div class="head">
									<span class="col">序号</span>
									<span class="col">所属名</span>
									<span class="col">设备名称</span>
									<span class="col">报警时间</span>
									<span class="col">所属单位</span>
									<span class="col">故障原因</span>
									<span class="col">排查建议</span>
									<span class="col">操作</span>
								</div>
								<div class="scroll-view" ref="scrollViewRef" @mouseenter="onMouseenter"
									@mouseleave="onMouseleave">
									<div v-if="deviceFaultsList.length > 6" class="marquee-view" ref="listRef"
										v-for="(p, n) in 2" :key="n">
										<div class="marquee" v-for="(item, index) in deviceFaultsList" :key="index">
											<div class="row">
												<span class="col">{{ index + 1 }}</span>
												<span class="col">{{ item.belongsDeviceName }}</span>
												<span class="col">{{ item.deviceName }}</span>
												<span class="col">{{ item.alertTime }}</span>
												<span class="col">{{ item.groupName }}</span>
												<span class="col">{{ item.faultReason }}</span>
												<span class="col">{{ item.suggestion }}</span>
												<span class="col">排查 详情</span>
												<span class="icon-dot"></span>
											</div>

										</div>
									</div>
									<div v-else class="marquee-view" ref="listRef">
										<div class="marquee" v-for="(item, index) in deviceFaultsList" :key="index">
											<div class="row">
												<span class="col">{{ index + 1 }}</span>
												<span class="col">{{ item.belongsDeviceName }}</span>
												<span class="col">{{ item.deviceName }}</span>
												<span class="col">{{ item.alertTime }}</span>
												<span class="col">{{ item.groupName }}</span>
												<span class="col">{{ item.faultReason }}</span>
												<span class="col">{{ item.suggestion }}</span>
												<span class="col">排查 详情</span>
												<span class="icon-dot"></span>
											</div>

										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 右边 -->
				<div class="big-data-down-right">
					<!-- 右1 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备周故障对比</span>
								<!-- <span class="flex-title-small">单位：次</span> -->
							</div>
							<!-- <div class="top-RightOne">
								<div class="content leftcss">
									<div class="title">上周</div>
									<div class="num">82</div>
								</div>
								<div class="center">VS</div>
								<div class="content rightcss">
									<div class="num">0</div>
									<div class="title">本周</div>
								</div>
							</div> -->
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightOneRef"></div>
							</div>
						</div>
					</div>
					<!-- 右2 -->
					<!-- <div class="flex-warp-item">
						<div class="flex-warp-item-box">
							<div class="flex-title">设备运行统计</div>
							<div class="flex-content">
								<div class="task">
									<div class="task-item task-first-item">
										<div class="task-item-value task-first">25</div>
										<div class="task-item-label">待办任务</div>
									</div>
									<div class="task-item">
										<div class="task-item-box task1">
											<div class="task-item-value">12</div>
											<div class="task-item-label">施肥</div>
										</div>
									</div>
									<div class="task-item">
										<div class="task-item-box task2">
											<div class="task-item-value">3</div>
											<div class="task-item-label">施药</div>
										</div>
									</div>
									<div class="task-item">
										<div class="task-item-box task3">
											<div class="task-item-value">5</div>
											<div class="task-item-label">农事</div>
										</div>
									</div>
								</div>
								<div class="progress">
									<div class="progress-item">
										<span>施肥率</span>
										<div class="progress-box">
											<el-progress :percentage="70" color="#43bdf0"></el-progress>
										</div>
									</div>
									<div class="progress-item">
										<span>施药率</span>
										<div class="progress-box">
											<el-progress :percentage="36" color="#43bdf0"></el-progress>
										</div>
									</div>
									<div class="progress-item">
										<span>农事率</span>
										<div class="progress-box">
											<el-progress :percentage="91" color="#43bdf0"></el-progress>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flex-content" style="color: red;">
							<div style="height: 100%" ref="chartsRightTwoRef"></div>
						</div>
					</div> -->
					<!-- 右2 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border" style="position: relative;">
							<div class="flex-title">
								<span>设备运行统计</span>
							</div>
							<div style="position: absolute; right: 2%; top: 4%;">
								<!-- <el-select class="my-select" popper-class="mySelectStyle" size="small"
									v-model="selectvalue.groupId" clearable style="width: 140px;color: red!important;">
									<el-option v-for="item in options" :key="item.groupId" :label="item.groupName"
										:value="item.groupId" />
								</el-select> -->
								<el-tree-select class="my-select" popper-class="mySelectStyle"
									v-model="selectvalue.groupId" :data="options" :props="defaultProps" check-strictly
									:render-after-expand="true" style="width: 200px" :show-count="true" size="small"
									@node-click="getrunStatisticsList" />
							</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightTwoRef"></div>
							</div>
						</div>
					</div>
					<!-- 右3 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备运维考核</span>
							</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightThreeRef"></div>
							</div>
						</div>
					</div>
					<!-- 右4 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备故障区域排名</span>
								<!-- <span class="flex-title-small">单位：件</span> -->
							</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightFourRef"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="chart-warp-bottom" v-else-if="activeTab == 1">
				<ops-management></ops-management>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="chartIndex">
import { defineAsyncComponent, reactive, onMounted, watch, nextTick, onActivated, ref, onUnmounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import 'echarts-wordcloud';
import { storeToRefs } from 'pinia';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { Affiliation, assessEvaluation, completenessRate, faultsList, faultsMalfunction, faultsWeek, MonitorDynamics, MonitorRang, regionalRankings, runStatistics } from './chart';
import { handleTree } from '/@/utils/next';
import opsManagement from './opsManagement.vue';
import ThreeScene from '/@/components/threejs/ThreeScene.vue';
const storesTagsViewRoutes = useTagsViewRoutes();
const { isTagsViewCurrenFull } = storeToRefs(storesTagsViewRoutes);

// 引入组件
const ChartHead = defineAsyncComponent(() => import('/@/views/chart/head.vue'));
// 定义变量内容
const state = reactive({
	myCharts: [] as EmptyArrayType,
});
const timeout = ref()
const timeout1 = ref()
const isFullscreen = ref(false);  // 控制全屏状态
const activeTab = ref(0); // 控制选中tab
// const chartsLeftOneRef = ref();// 左1 
const chartsLeftTwoRef = ref();//左2 设备监测范围
let myChart1 = null as any; // 设备监测动态
let myChart2 = null as any; // 设备故障分类对比
let myChart3 = null as any; // 采集齐全率分析
let myChart4 = null as any; // 设备周故障对比
let myChart5 = null as any; // 设备运行统计
let myChart6 = null as any; // 设备运维考核
let myChart7 = null as any; // 设备故障区域排名
const chartsLeftThreeRef = ref();//设备故障分类对比 左3
const chartsLeftFourRef = ref();//采集齐全率分析 左4
const chartsCenterOneRef = ref();//中 1
const deviceFaultsList = ref([
	{
		belongsDeviceName: "欢127-16-31",
		deviceName: "温压一体仪",
		alertTime: "2025-03-28 13:30",
		groupName: "采油作业二区",
		faultReason: "设备故障异常值：...",
		suggestion: "建议一：...;建议二：..."
	},
	{
		belongsDeviceName: "欢127-16-31",
		deviceName: "智能电参",
		alertTime: "2025-03-28 13:30",
		groupName: "采油作业二区",
		faultReason: "设备故障异常值：...",
		suggestion: "建议一：...;建议二：..."
	},
	{
		belongsDeviceName: "欢127-16-31",
		deviceName: "智能电参",
		alertTime: "2025-03-28 13:30",
		groupName: "采油作业二区",
		faultReason: "设备故障异常值：...",
		suggestion: "建议一：...;建议二：..."
	},
	{
		belongsDeviceName: "欢127-16-31",
		deviceName: "智能电参",
		alertTime: "2025-03-28 13:30",
		groupName: "采油作业二区",
		faultReason: "设备故障异常值：...",
		suggestion: "建议一：...;建议二：..."
	},
	{
		belongsDeviceName: "欢127-16-31",
		deviceName: "智能电参",
		alertTime: "2025-03-28 13:30",
		groupName: "采油作业二区",
		faultReason: "设备故障异常值：...",
		suggestion: "建议一：...;建议二：..."
	},
	{
		belongsDeviceName: "欢127-16-31",
		deviceName: "智能电参",
		alertTime: "2025-03-28 13:30",
		groupName: "采油作业二区",
		faultReason: "设备故障异常值：...",
		suggestion: "建议一：...;建议二：..."
	},
	{
		belongsDeviceName: "欢127-16-31",
		deviceName: "智能电参",
		alertTime: "2025-03-28 13:30",
		groupName: "采油作业二区",
		faultReason: "设备故障异常值：...",
		suggestion: "建议一：...;建议二：..."
	},


]);//中 2
const listRef = ref(); //列表dom
const scrollViewRef = ref(); //滚动区域dom
let intervalId = ref(); //滚动定时器
let isAutoScrolling = ref(true); //是否自动滚动标识
const chartsRightOneRef = ref();//右1 设备周故障对比
const chartsRightTwoRef = ref();//右2 设备运行统计
const chartsRightThreeRef = ref();//右3 设备运维考核
const chartsRightFourRef = ref();//右4 设备故障区域排名
const chartDatas = reactive([
	{
		name: '监控（在装）',
		num: '5240'
	},
	{
		name: '临时拆卸',
		num: '133'
	},
	{
		name: '长停回收',
		num: '148'
	},
	{
		name: '回收报废',
		num: '0'
	},
])
interface product {
	productId: number;
	deviceCount: number;
	productName: string;
}
let chartleftOneDatas = ref<product[]>([])  //左1 设备监测范围
let chartleftTwoDatas = ref({
	abnormalCountsList: [] as any,//异常设备数
	collectionRatesList: [] as any,//采集齐全率
	datesList: [] as any,//统计日期
	onlineCountsList: [] as any,//在线设备数
	totalCountsList: [] as any //设备总数
})  //左2 设备监测动态
let chartleftThreeDatas = ref([])  //左3 设备监测范围
let chartleftFourDatas = ref({
	abnormalCountsList: [] as any,//异常设备数
	collectionRatesList: [] as any,//采集齐全率
	placesList: [] as any,//统计日期
	normalCountsList: [] as any,//正常设备数
	totalCountsList: [] as any //设备总数
})  //左4 采集齐全率分析
let chartRightOneDatas = ref({
	namesList: [] as any,//设备名称
	lastWeekDeviceCountsList: [] as any,//上周
	thisWeekDeviceCountsList: [] as any,//本周
})  //右1 设备周故障对比
const selectvalue = ref({
	groupId: '' as any,
	groupName: '',
}) //下拉框数据
let chartRightTwoDatas = ref({
	namesList: [] as any,//设备名称
	deviceNormalCountsList: [] as any,//正常数量
	deviceFaultsCountsList: [] as any,//故障数量
})  //右2 设备运行统计
let chartRightThreeDatas = ref({
	DatesList: [] as any,//设备名称
	handledFaultsCountsList: [] as any,//故障处置数
	deviceFaultsCountsList: [] as any,//设备故障数
})  //右3 设备运维考核
let chartRightFourDatas = ref({
	namesList: [] as any,//设备名称
	deviceFaultsCountsList: [] as any,//设备故障数
})  //右3 设备运维考核
const defaultProps = {
	children: 'children',
	label: 'groupName',
	value: 'groupId',
}
let options = ref([
	{
		groupId: 1111,
		groupName: `select1`,
	},
	{
		groupId: 222,
		groupName: `select2`,
	},
]
)


// 切换全屏模式
const toggleFullScreen = () => {
	const elem = document.getElementById('dom') as any
	if (document.fullscreenElement) {
		document.exitFullscreen();
		isFullscreen.value = false;
	} else {
		if (elem.requestFullscreen) {
			elem.requestFullscreen();
		} else if (elem.webkitRequestFullscreen) { // Safari
			elem.webkitRequestFullscreen();
		} else if (elem.mozRequestFullScreen) { // Firefox
			elem.mozRequestFullScreen();
		} else if (elem.msRequestFullscreen) { // IE/Edge
			elem.msRequestFullscreen();
		}
		isFullscreen.value = true;
	}

};
// 获取页面显示情况
const getShowActive = (active: any) => {
	if (active == 0) {
		activeTab.value = 0

		nextTick(() => {
			allchartsinit()
			getAllList()
			autoScrolling();
		});
	} else {
		activeTab.value = active
		myChart1.dispose(); // 销毁图表实例
		myChart2.dispose(); // 销毁图表实例
		myChart3.dispose(); // 销毁图表实例
		myChart4.dispose(); // 销毁图表实例
		myChart5.dispose(); // 销毁图表实例
		myChart6.dispose(); // 销毁图表实例
		myChart7.dispose(); // 销毁图表实例
		clearInterval(timeout.value);    // 清除单次定时器
		clearInterval(timeout1.value);   // 清除单次定时器
		clearInterval(intervalId.value); // 清除循环定时器
	}

}
// 设备检测范围 左1
const initChartsLeftOne = () => {

};
// 获取监控范围数据数据
const getMonitorRangList = async () => {
	const response = await MonitorRang()
	chartleftOneDatas.value = response.data.data[0].productDeviceCounts

}


// 初始化设备监控动态数据 左2
const initChartsLeftTwo = () => {
	const handleResize = () => {
		if (myChart1) {
			myChart1.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	const resizeObserver = new ResizeObserver(handleResize);
	resizeObserver.observe(chartsLeftTwoRef.value);
	myChart1 = echarts.init(chartsLeftTwoRef.value);
	const option = {
		grid: {
			// top: '30%',
			// right: 20,
			bottom: '13%',
			// left: 30,
		},
		tooltip: {
			trigger: 'axis',
			extraCssText: 'cursor: url(./images/pointer.png) 8 3, auto !important;',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			}
		},
		legend: {
			top: '5%',
			data: ['设备总数', '在线', '异常', '采集齐全率'],
			// 隐藏图例边框
			itemStyle: {
				// 去掉图例项的边框
				borderWidth: 0,  // 图例项没有边框
				borderColor: 'transparent'  // 图例项边框颜色为透明
			},
			textStyle: {
				color: '#f7f7f7',
			}
		},
		xAxis: [
			{
				type: 'category',
				data: chartleftTwoDatas.value.datesList,
				axisPointer: {
					type: 'shadow'
				},
				axisLabel: {
					interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
					// rotate: 38   //调整数值改变倾斜的幅度（范围-90到90）
					color: '#ccc',
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#2f4866',
						type: 'dashed',
					}
				},
				axisTick: {
					show: false, // (刻度线)
				},
			}
		],
		yAxis: [
			{
				type: 'value',
				minInterval: 1,
				name: '设备数',
				min: 0,
				// max: 6000,
				// interval: 1000,
				alignTicks: true,
				nameTextStyle: {
					color: '#ccc',
				},
				axisTick: {
					show: false, // (刻度线)
				},
				axisLabel: {
					formatter: '{value}',
					color: '#ccc',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',  // 设置网格线为虚线
						color: '#2f4866'    // 设置网格线颜色
					}
				},


			},
			{
				type: 'value',
				name: '采集齐全率',
				min: 0,
				// max: 100,
				// interval: 20,
				alignTicks: true,
				nameTextStyle: {
					color: '#ccc',
				},
				axisLabel: {
					formatter: '{value}%',
					color: '#ccc',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',  // 设置网格线为虚线
						color: '#2f4866'    // 设置网格线颜色
					}
				}
			}
		],
		series: [
			{
				name: '设备总数',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftTwoDatas.value.totalCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2574d6' },   // 起始颜色
						{ offset: 0, color: '#0b2f50' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2574d6', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '在线',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftTwoDatas.value.onlineCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#dbbd3f' },   // 起始颜色
						{ offset: 0, color: '#364d4c' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#dbbd3f', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '异常',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftTwoDatas.value.abnormalCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2cb5cc' },   // 起始颜色
						{ offset: 0, color: '#134b67' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2cb5cc', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '采集齐全率',
				type: 'line',
				yAxisIndex: 1,
				smooth: true,  // 设置为 true，使得折线带有弧度
				tooltip: {
					valueFormatter: (value: any) => {
						return value + "%"; // 在 tooltip 中显示百分比
					}
				},
				data: chartleftTwoDatas.value.collectionRatesList,
				label: {
					show: true,
					position: 'top',
					color: '#1bb24e'
				},
				itemStyle: {

					color: '#24b256', // 折线点的颜色
					lineStyle: {
						width: 3,
						color: '#1aae4d' // 折线颜色
					}

				}
			}
		]
	};
	myChart1.setOption(option);
	state.myCharts.push(myChart1);
};
// 更新设备监控动态数据 左2
const updataChartsLeftTwo = () => {
	myChart1.setOption({
		xAxis: [
			{
				data: chartleftTwoDatas.value.datesList,

			}
		],
		series: [
			{
				data: chartleftTwoDatas.value.totalCountsList,
			},
			{
				data: chartleftTwoDatas.value.onlineCountsList,
			},
			{
				data: chartleftTwoDatas.value.abnormalCountsList,
			},
			{
				data: chartleftTwoDatas.value.collectionRatesList,
			}
		]
	});
}
// 获取设备监控动态数据 左2
const getMonitorDynamicsList = async () => {
	const response = await MonitorDynamics()
	const abnormalCountsList = [] as any
	const collectionRatesList = [] as any
	const datesList = [] as any
	const onlineCountsList = [] as any
	const totalCountsList = [] as any
	response.data.data.forEach((item: any) => {
		abnormalCountsList.push(Number(item.abnormalCount))
		collectionRatesList.push(Number(item.collectionRate.replace('%', '')))
		datesList.push(item.date)
		onlineCountsList.push(Number(item.onlineCount))
		totalCountsList.push(Number(item.totalCount))
	})
	chartleftTwoDatas.value.abnormalCountsList = abnormalCountsList
	chartleftTwoDatas.value.collectionRatesList = collectionRatesList
	chartleftTwoDatas.value.datesList = datesList
	chartleftTwoDatas.value.onlineCountsList = onlineCountsList
	chartleftTwoDatas.value.totalCountsList = totalCountsList
	console.log(chartleftTwoDatas.value, '设备监测动态');
	updataChartsLeftTwo()

}

// 初始化设备故障分类对比数据 左3
const initChartsLeftThree = () => {
	const handleResize = () => {
		if (myChart2) {
			myChart2.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	const resizeObserver = new ResizeObserver(handleResize);
	resizeObserver.observe(chartsLeftThreeRef.value);
	myChart2 = echarts.init(chartsLeftThreeRef.value);
	const option = {
		tooltip: {
			trigger: 'item',
			formatter: '{a} <br/>{b}: {c} ({d}%)'
		},
		series: [
			{
				name: '设备故障分类对比',
				type: 'pie',
				radius: [45, 70],
				center: ['50%', '50%'],
				// roseType: 'area',
				itemStyle: {
					borderRadius: 1,
				},
				data: chartleftThreeDatas.value,
				label: {
					show: true,  // 显示标签
					position: 'outside', // 标签位置：outside（外部）、inside（内部）
					color: '#ccc',  // 设置标签文字颜色
					fontSize: 12, // 设置文字大小
					formatter: '{b}\n{d}%',  // 显示名称和百分比，换行显示
				},
				emphasis: {
					label: {
						show: true,
						color: '#ffffff'  // 鼠标悬浮时文字颜色
					}
				}
			},
		],
	};
	myChart2.setOption(option);
	state.myCharts.push(myChart2);
};
//更新设备故障分类对比数据 左3
const updataChartsLeftThree = () => {
	myChart2.setOption({
		series: [
			{
				data:  chartleftThreeDatas.value,
			},
		],
	});
}
// 获取设备故障分类对比数据 左3
const getFaultsMalfunctionList = async () => {
	const response = await faultsMalfunction()
	const data = response.data.data.faultDeviceCounts.map((item: any) => {
		let newItem = {} as any;
		Object.entries(item).forEach(([key, value]) => {
			// 根据旧键名修改成新键名
			if (key === 'productName') {
				newItem.name = value;
			} else if (key === 'deviceFaultsCount') {
				newItem.value = value;
			} else {
				newItem[key] = value;  // 其他键保持不变
			}
		});
		return newItem;
	})
	chartleftThreeDatas.value = data.filter((item: { value: number; }) => item.value > 0);
	console.log(chartleftThreeDatas.value, '设备故障分类对比');

	updataChartsLeftThree()
}


// 初始化采集齐全率分析数据 左4
const initChartsLeftFour = () => {
	const handleResize = () => {
		if (myChart3) {
			myChart3.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	const resizeObserver = new ResizeObserver(handleResize);
	resizeObserver.observe(chartsLeftFourRef.value);
	myChart3 = echarts.init(chartsLeftFourRef.value);
	const option = {
		grid: {
			top: '28%',
			// right: '10%',
			bottom: '25%',
			left: '10%'
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			},
		},
		legend: {
			top: '3%',
			data: ['设备总数', '正常', '异常', '采集齐全率'],
			// 隐藏图例边框
			itemStyle: {
				// 去掉图例项的边框
				borderWidth: 0,  // 图例项没有边框
				borderColor: 'transparent'  // 图例项边框颜色为透明
			},
			textStyle: {
				color: '#f7f7f7',
			}
		},
		xAxis: [
			{
				type: 'category',
				data: chartleftFourDatas.value.placesList,
				axisPointer: {
					type: 'shadow'
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#2f4866',
						type: 'dashed',
					}
				},
				axisTick: {
					show: false, // (刻度线)
				},
				axisLabel: {
					interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
					rotate: 28,  //调整数值改变倾斜的幅度（范围-90到90）
					color: '#ccc',
				}
			}
		],
		yAxis: [
			{
				type: 'value',
				minInterval: 1,
				name: '监控数量',
				nameTextStyle: {
					color: '#ccc',
				},
				min: 0,
				alignTicks: true,
				axisLabel: {
					formatter: '{value}',
					color: '#ccc',

				},
				splitLine: {
					lineStyle: {
						type: 'dashed',  // 设置网格线为虚线
						color: '#2f4866'    // 设置网格线颜色
					}
				}
			},
			{
				type: 'value',
				name: '采集齐全率',
				min: 0,
				alignTicks: true,
				nameTextStyle: {
					color: '#ccc',
				},
				axisLabel: {
					formatter: '{value}%',
					color: '#ccc',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',  // 设置网格线为虚线
						color: '#2f4866'    // 设置网格线颜色
					}
				},

			}
		],
		series: [
			{
				name: '设备总数',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftFourDatas.value.totalCountsList,
				// data: [1, 2, 3, 4, 4, 4, 4,],
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2574d6' },   // 结束颜色
						{ offset: 0, color: '#0b2f50' }// 起始颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2574d6', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '正常',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftFourDatas.value.normalCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#dbbd3f' },   // 起始颜色
						{ offset: 0, color: '#364d4c' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#dbbd3f', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '异常',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftFourDatas.value.abnormalCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2cb5cc' },   // 起始颜色
						{ offset: 0, color: '#134b67' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2cb5cc', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '采集齐全率',
				type: 'line',
				yAxisIndex: 1,
				smooth: true,  // 设置为 true，使得折线带有弧度
				tooltip: {
					valueFormatter: (value: any) => {
						return value + "%";
					}
				},
				data: chartleftFourDatas.value.collectionRatesList,
				// data: [0, 0, 0, 0, 0, 0.2, 1],
				label: {
					show: true,
					position: 'top',
					color: '#1bb24e'
				},
				itemStyle: {
					color: '#24b256', // 折线点的颜色
					lineStyle: {
						width: 3,
						color: '#1aae4d' // 折线颜色
					}

				}
			}
		]
	};
	myChart3.setOption(option);
	state.myCharts.push(myChart3);
};
// 更新采集齐全率分析数据 左4
const updataChartsLeftFour = () => {
	myChart3.setOption({
		xAxis: [
			{
				data: chartleftFourDatas.value.placesList,
			}
		],
		series: [
			{
				data: chartleftFourDatas.value.totalCountsList,

			},
			{
				data: chartleftFourDatas.value.normalCountsList,

			},
			{
				data: chartleftFourDatas.value.abnormalCountsList,

			},
			{
				data: chartleftFourDatas.value.collectionRatesList,
			}
		]
	});
}
// 获取采集齐全率分析数据 左4
const getCompletenessRate = async () => {
	const response = await completenessRate()
	const abnormalCountsList = [] as any
	const collectionRatesList = [] as any
	const placesList = [] as any
	const normalCountsList = [] as any
	const totalCountsList = [] as any
	response.data.data.forEach((item: any) => {
		abnormalCountsList.push(Number(item.abnormalCount))
		collectionRatesList.push(Number(item.collectionRate.replace('%', '')))
		placesList.push(item.groupName)
		normalCountsList.push(Number(item.onlineCount))
		totalCountsList.push(Number(item.totalCount))
	})
	chartleftFourDatas.value.abnormalCountsList = abnormalCountsList
	chartleftFourDatas.value.collectionRatesList = collectionRatesList
	chartleftFourDatas.value.placesList = placesList
	chartleftFourDatas.value.normalCountsList = normalCountsList
	chartleftFourDatas.value.totalCountsList = totalCountsList
	console.log(chartleftFourDatas.value, '采集齐全率');
	updataChartsLeftFour()
}

// 初始化中间图表1
const initChartsCenterOne = () => {
	const myChart = echarts.init(chartsCenterOneRef.value);
	const option = {
		grid: {
			top: 15,
			right: 15,
			bottom: 20,
			left: 30,
		},
		tooltip: {},
		series: [
			{
				type: 'wordCloud',
				sizeRange: [12, 40],
				rotationRange: [0, 0],
				rotationStep: 45,
				gridSize: Math.random() * 20 + 5,
				shape: 'circle',
				width: '100%',
				height: '100%',
				textStyle: {
					fontFamily: 'sans-serif',
					fontWeight: 'bold',
					color: function () {
						return `rgb(${[Math.round(Math.random() * 160), Math.round(Math.random() * 160), Math.round(Math.random() * 160)].join(',')})`;
					},
				},
				data: [
					{ name: 'vue-next-admin', value: 520 },
					{ name: 'lyt', value: 520 },
					{ name: 'next-admin', value: 500 },
					{ name: '更名', value: 420 },
					{ name: '智慧农业', value: 520 },
					{ name: '男神', value: 2.64 },
					{ name: '好身材', value: 4.03 },
					{ name: '校草', value: 24.95 },
					{ name: '酷', value: 4.04 },
					{ name: '时尚', value: 5.27 },
					{ name: '阳光活力', value: 5.8 },
					{ name: '初恋', value: 3.09 },
					{ name: '英俊潇洒', value: 24.71 },
					{ name: '霸气', value: 6.33 },
					{ name: '腼腆', value: 2.55 },
					{ name: '蠢萌', value: 3.88 },
					{ name: '青春', value: 8.04 },
					{ name: '网红', value: 5.87 },
					{ name: '萌', value: 6.97 },
					{ name: '认真', value: 2.53 },
					{ name: '古典', value: 2.49 },
					{ name: '温柔', value: 3.91 },
					{ name: '有个性', value: 3.25 },
					{ name: '可爱', value: 9.93 },
					{ name: '幽默诙谐', value: 3.65 },
				],
			},
		],
	};
	myChart.setOption(option);
	state.myCharts.push(myChart);
};
// 获取设备故障清单 中2
const getfaultsList = async () => {
	const response = await faultsList()
	response.data.rows.forEach((item: any) => {
		let date = new Date(item.alertTime);
		// 获取日期部分
		let year = date.getFullYear();
		let month = (date.getMonth() + 1).toString().padStart(2, '0');
		let day = date.getDate().toString().padStart(2, '0');
		// 获取时间部分
		let hours = date.getHours().toString().padStart(2, '0');
		let minutes = date.getMinutes().toString().padStart(2, '0');
		item.alertTime = `${year}-${month}-${day} ${hours}:${minutes}`;
	})
	deviceFaultsList.value = response.data.rows


};
//设置自动滚动 中2
const autoScrolling = () => {
	intervalId.value = setInterval(() => {
		if (scrollViewRef.value && listRef.value && listRef.value[0]) {
			if (scrollViewRef.value.scrollTop < listRef.value[0].clientHeight) {
				scrollViewRef.value.scrollTop += isAutoScrolling.value ? 1 : 0;
			} else {
				scrollViewRef.value.scrollTop = 0;
			}
		}
	}, 20);
};


// 初始化设备故障对比数据 右1
const initChartsRightOne = () => {
	const handleResize = () => {
		if (myChart4) {
			myChart4.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	const resizeObserver = new ResizeObserver(handleResize);
	resizeObserver.observe(chartsRightOneRef.value);
	myChart4 = echarts.init(chartsRightOneRef.value);

	// let leftData = [1, 0, 0, 1, 3, 9, 50, 7, 5, 6]
	// let rightData = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
	// const leftmax = Math.max(...leftData);
	// const rightmax = Math.max(...rightData);
	// const option = {
	// 	// color:colors,
	// 	tooltip: {
	// 		show: true,
	// 		trigger: "axis",
	// 		axisPointer: {
	// 			type: "shadow",

	// 		},
	// 		formatter: function (params: any) {
	// 			// console.log("1222", params);
	// 			return params
	// 		},
	// 	},
	// 	grid: [
	// 		// 左
	// 		{
	// 			top: "3%",
	// 			// bottom: "2%",
	// 			left: "0%",
	// 			width: "36%",
	// 			height: "105%",
	// 			containLabel: true,
	// 		},
	// 		// 中
	// 		{
	// 			top: "4%",
	// 			bottom: "2%",
	// 			left: "50%",
	// 			// left: "center",
	// 			width: "50%",
	// 			height: "93%",
	// 		},
	// 		// 右
	// 		{
	// 			top: "3%",
	// 			left: "60%",
	// 			// bottom: "2%",
	// 			// right: "-20%",
	// 			width: "36%",
	// 			height: "105%",
	// 			containLabel: true,
	// 		},
	// 	],
	// 	xAxis: [
	// 		{
	// 			type: "value",
	// 			inverse: true,
	// 			show: false,
	// 			min: 0,
	// 			max: leftmax > rightmax ? leftmax : rightmax,
	// 		},
	// 		{
	// 			gridIndex: 1,
	// 			show: false,
	// 		},
	// 		{
	// 			gridIndex: 2,
	// 			type: "value",
	// 			min: 0,
	// 			max: leftmax > rightmax ? leftmax : rightmax,
	// 			show: false,

	// 		},
	// 	],
	// 	yAxis: [
	// 		{
	// 			axisLabel: {
	// 				show: false,
	// 			},
	// 			type: "category",
	// 			axisTick: {
	// 				show: false,//y轴刻度线
	// 			},
	// 			axisLine: {
	// 				//轴线
	// 				lineStyle: {
	// 					color: "#1057e4",
	// 					type: 'solid'
	// 				},
	// 			},
	// 			// data: ["有毒气体检测", "流量自控仪", "频率测量仪", "液位计", "可燃气体报警器", "温度变送器", "压力变送器", "智能电参", "温压一体仪", "示功仪"],
	// 		},
	// 		{
	// 			axisLabel: {
	// 				color: "#ccc",
	// 				fontSize: "12",
	// 				align: 'center', // 设置Y轴标签居中对齐
	// 			},
	// 			gridIndex: 1,
	// 			position: "center",
	// 			axisLine: {
	// 				show: false,
	// 			},
	// 			// type: "category",
	// 			inverse: false,
	// 			axisTick: {
	// 				//y轴刻度线
	// 				show: false,
	// 			},
	// 			data: [
	// 				"示功仪  ",
	// 				"温压一体仪 ",
	// 				"智能电参",
	// 				"压力变送器",
	// 				"温度变送器",
	// 				"可燃气体报警器",
	// 				"液位计",
	// 				"频率测量仪",
	// 				"流量自控仪",
	// 				"有毒气体检测",
	// 			], //使用空格使文字居中

	// 		},
	// 		{
	// 			gridIndex: 2,
	// 			axisLabel: {
	// 				show: false,
	// 			},
	// 			type: "category",
	// 			inverse: false,
	// 			axisTick: {
	// 				//y轴刻度线
	// 				show: false,
	// 			},
	// 			axisLine: {
	// 				//轴线
	// 				lineStyle: {
	// 					color: "#08786a",
	// 					type: 'solid'
	// 				},
	// 			},
	// 			// data: ["有毒气体检测", "流量自控仪", "频率测量仪", "液位计", "可燃气体报警器", "温度变送器", "压力变送器", "智能电参", "温压一体仪", "示功仪"],
	// 		},
	// 	],
	// 	series: [
	// 		{
	// 			type: "bar",
	// 			barWidth: 11,
	// 			itemStyle: {
	// 				borderRadius: [10, 0, 0, 10], // [左上, 右上, 左下, 右下] 四个角的圆角半径
	// 				color: "#1057e4",
	// 			},
	// 			label: {
	// 				show: true,
	// 				color: '#ffffff',
	// 				position: 'inside',
	// 			},
	// 			data: leftData,
	// 		},
	// 		{
	// 			type: "bar",
	// 			barWidth: 11,
	// 			xAxisIndex: 2,
	// 			yAxisIndex: 2,
	// 			itemStyle: {
	// 				borderRadius: [0, 10, 10, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
	// 				color: "#087668",
	// 			},
	// 			tooltip: {
	// 				axisPointer: {
	// 					type: 'shadow',
	// 					shadowStyle: {
	// 						opacity: 1  // 只去掉这一组数据的阴影效果
	// 					}
	// 				}
	// 			},
	// 			label: {
	// 				show: true,
	// 				color: '#ffffff',
	// 			},
	// 			data: rightData,
	// 		},
	// 	],
	// };
	// const option = {
	// 	tooltip: {
	// 		trigger: 'axis',
	// 		axisPointer: {
	// 			type: 'cross',
	// 			crossStyle: {
	// 				color: '#999'
	// 			}
	// 		}
	// 	},
	// 	grid: {
	// 		top: '20%',
	// 		right: '5%',
	// 		bottom: '30%',
	// 		left: '10%',
	// 	},
	// 	legend: {
	// 		data: ['本周', '上周',],
	// 		// 隐藏图例边框
	// 		itemStyle: {
	// 			// 去掉图例项的边框
	// 			borderWidth: 0,  // 图例项没有边框
	// 			borderColor: 'transparent'  // 图例项边框颜色为透明
	// 		},
	// 		textStyle: {
	// 			color: '#f7f7f7',
	// 		}
	// 	},
	// 	xAxis: [
	// 		{
	// 			type: 'category',
	// 			data: ["有毒气体检测", "流量自控仪", "频率测量仪", "液位计", "可燃气体报警器", "温度变送器", "压力变送器", "智能电参", "温压一体仪", "示功仪"],
	// 			axisPointer: {
	// 				type: 'shadow'
	// 			},
	// 			axisLabel: {
	// 				interval: 2, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
	// 				// rotate: 38,   //调整数值改变倾斜的幅度（范围-90到90）
	// 				color: '#ccc',
	// 			},
	// 			axisLine: {
	// 				show: true,
	// 				lineStyle: {
	// 					color: '#2f4866',
	// 					type: 'dashed',
	// 				}
	// 			},
	// 			axisTick: {
	// 				show: false, // (刻度线)
	// 			},
	// 		}
	// 	],
	// 	yAxis: [
	// 		{
	// 			type: 'value',
	// 			name: '故障数',
	// 			min: 0,
	// 			axisLabel: {
	// 				formatter: '{value}'
	// 			},
	// 			nameTextStyle: {
	// 				color: '#ccc',
	// 			},
	// 			axisTick: {
	// 				show: false, // (刻度线)
	// 			},
	// 			axisLabel: {
	// 				formatter: '{value}',
	// 				color: '#ccc',
	// 			},
	// 			splitLine: {
	// 				lineStyle: {
	// 					type: 'dashed',  // 设置网格线为虚线
	// 					color: '#2f4866'    // 设置网格线颜色
	// 				}
	// 			},
	// 		},
	// 	],
	// 	series: [
	// 		{
	// 			name: '上周',
	// 			type: 'bar',
	// 			tooltip: {
	// 				valueFormatter: function (value) {
	// 					return value;
	// 				}
	// 			},
	// 			data: [1, 0, 0, 1, 3, 9, 50, 7, 5, 6],
	// 			label: {
	// 				show: true,
	// 				position: 'top',
	// 				color: '#ffffff'
	// 			},
	// 			itemStyle: {
	// 				borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
	// 				// 使用线性渐变色
	// 				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
	// 					{ offset: 0, color: '#0b2f50' },   // 起始颜色
	// 					{ offset: 1, color: '#2574d6' }// 结束颜色
	// 				]),
	// 				// 设置边框颜色为固定颜色
	// 				borderColor: '#2574d6', // 边框固定颜色
	// 				borderWidth: 2,
	// 			}
	// 		},
	// 		{
	// 			name: '本周',
	// 			type: 'bar',
	// 			tooltip: {
	// 				valueFormatter: function (value) {
	// 					return value;
	// 				}
	// 			},
	// 			data: [2, 1, 4, 6, 8, 9, 25, 12, 33, 8],
	// 			label: {
	// 				show: true,
	// 				position: 'top',
	// 				color: '#ffffff'
	// 			},
	// 			itemStyle: {
	// 				borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
	// 				// 使用线性渐变色
	// 				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
	// 					{ offset: 0, color: '#364d4c' },   // 起始颜色
	// 					{ offset: 1, color: '#dbbd3f' }// 结束颜色
	// 				]),
	// 				// 设置边框颜色为固定颜色
	// 				borderColor: '#dbbd3f', // 边框固定颜色
	// 				borderWidth: 2,
	// 			}

	// 		},
	// 	]
	// };
	const option = {
		grid: {
			top: '20%',
			right: '5%',
			bottom: '15%',
			left: '10%'
		},
		tooltip: {
			trigger: 'axis'
		},
		legend: {
			show: true,
			top: '5%',
			data: ['上周', '本周'],
			// 隐藏图例边框
			itemStyle: {
				// 去掉图例项的边框
				borderWidth: 0,  // 图例项没有边框
				borderColor: 'transparent'  // 图例项边框颜色为透明
			},
			textStyle: {
				color: '#f7f7f7',
			}
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: chartRightOneDatas.value.namesList,
			axisLine: {
				show: true,
				lineStyle: {
					color: '#2f4866',
					type: 'dashed',
				}
			},
			axisTick: {
				show: false, // (刻度线)
			},
			axisLabel: {
				interval: 1, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
				// rotate: 38,  //调整数值改变倾斜的幅度（范围-90到90）
				color: '#ccc',
			}
		},
		yAxis: {
			type: 'value',
			minInterval: 1,
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',

			},
			splitLine: {
				lineStyle: {
					type: 'dashed',  // 设置网格线为虚线
					color: '#2f4866'    // 设置网格线颜色
				}
			}
		},
		series: [
			{
				name: '上周',
				type: 'line',
				data: chartRightOneDatas.value.lastWeekDeviceCountsList,
				smooth: true,  // 设置为 true，使得折线带有弧度
				lineStyle: {
					width: 3,
					color: '#00afff' // 折线颜色
				},
				areaStyle: {

					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
						offset: 0,
						color: 'rgba(0, 175, 255, 0.8)'
					}, {
						offset: 0.8,
						color: 'rgba(0, 175, 255, 0.1)'
					}], false),
					shadowColor: 'rgba(0, 0, 0, 0.1)',

				},
			},
			{
				name: '本周',
				type: 'line',
				data: chartRightOneDatas.value.thisWeekDeviceCountsList,
				smooth: true,  // 设置为 true，使得折线带有弧度
				lineStyle: {
					width: 3,
					color: '#1aae4d' // 折线颜色
				},
				areaStyle: {

					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
						offset: 0,
						color: 'rgba(27, 190, 83,.8)'
					}, {
						offset: 0.8,
						color: 'rgba(27, 190, 83, 0.1)'
					}], false),
					shadowColor: 'rgba(0, 0, 0, 0.1)',

				},
			}
		]
	};
	myChart4.setOption(option);
	state.myCharts.push(myChart4);
};
// 更新设备故障对比数据 右1
const updataChartsRightOne = () => {
	myChart4.setOption({
		xAxis: [
			{
				data: chartRightOneDatas.value.namesList,
			}
		],
		series: [
			{
				data: chartRightOneDatas.value.lastWeekDeviceCountsList,
			},
			{
				data: chartRightOneDatas.value.thisWeekDeviceCountsList,
			}
		]
	});
}
// 获取设备故障周对比数据 右1
const getfaultsWeekList = async () => {
	const response = await faultsWeek()
	const namesList = [] as any
	const thisWeekDeviceCountsList = [] as any
	const lastWeekDeviceCountsList = [] as any
	response.data.data.thisWeekDeviceCounts.forEach((item: any) => {
		namesList.push(item.productName)
		thisWeekDeviceCountsList.push(item.deviceFaultsCount)
	})
	response.data.data.lastWeekDeviceCounts.forEach((item: any) => {
		lastWeekDeviceCountsList.push(item.deviceFaultsCount)
	})
	chartRightOneDatas.value.namesList = namesList
	chartRightOneDatas.value.thisWeekDeviceCountsList = thisWeekDeviceCountsList
	chartRightOneDatas.value.lastWeekDeviceCountsList = lastWeekDeviceCountsList
	console.log(chartRightOneDatas.value, '设备故障周对比');
	updataChartsRightOne()
}


// 初始化设备运行统计数据 右2
const initChartsRightTwo = () => {
	const handleResize = () => {
		if (myChart5) {
			myChart5.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	const resizeObserver = new ResizeObserver(handleResize);
	resizeObserver.observe(chartsRightTwoRef.value);
	myChart5 = echarts.init(chartsRightTwoRef.value);
	const option = {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		legend: {
			left: '3%',
			top: '5%',
			itemWidth: 15,
			itemHeight: 14,
			textStyle: {
				color: '#f7f7f7',
			}
		},
		grid: {
			top: '20%',
			left: '5%',
			right: '8%',
			bottom: '5%',
			containLabel: true
		},
		xAxis: {
			type: 'value',
			min: 0,
			// max: 1500,
			nameTextStyle: {
				color: '#ccc',
			},
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},
			splitLine: {
				show: false,
			}
		},
		yAxis: {
			type: 'category',
			data: chartRightTwoDatas.value.namesList,
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},
			axisTick: {
				show: false, // (刻度线)
			},
			splitLine: {
				show: true,
				lineStyle: {
					type: 'dashed',  // 设置网格线为虚线
					color: '#2f4866'    // 设置网格线颜色
				}
			},
			axisLine: {
				show: false,
			},

		},
		series: [
			{
				name: '正常数',
				type: 'bar',
				stack: 'total',
				barWidth: 11,
				label: {
					show: true
				},
				emphasis: {
					focus: 'series'
				},
				data: chartRightTwoDatas.value.deviceNormalCountsList,
				itemStyle: {
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 0, color: '#1e90ff' },   // 起始颜色
						{ offset: 1, color: '#134b67' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#1e90ff', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '故障数',
				type: 'bar',
				stack: 'total',
				label: {
					show: true,
					color: '#ffffff',
				},
				// itemStyle: {
				// 	color: '#ff69b4',
				// },
				emphasis: {
					focus: 'series'
				},
				data: chartRightTwoDatas.value.deviceFaultsCountsList,
				itemStyle: {
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 0, color: '#ef686a' },//  起始颜色
						{ offset: 1, color: '#134b67' },// 结束颜色

					]),
					// 设置边框颜色为固定颜色
					borderColor: '#ef686a', // 边框固定颜色
					borderWidth: 2
				}
			},

		]
	};
	// const option = {
	// 	tooltip: {
	// 		trigger: 'item',  // 触发类型为 'item'，表示仅显示当前鼠标悬停的数据项
	// 		formatter: function (params,index) {
	// 			let tooltipContent = `${params.name}:<br/>`;
	// 			params.value.forEach((val, index) => {
	// 				tooltipContent += `指标${index + 1}: ${val}<br/>`; // 显示每个指标的名称和对应的数值
	// 			});
	// 			return tooltipContent;
	// 		}
	// 	},
	// 	legend: {
	// 		show: false,
	// 		data: ['正常数', '故障数']
	// 	},
	// 	radar: {
	// 		// shape: 'circle', ['示功仪', '温压一体仪', '压力变送器', '温度变送器', '智能电参']
	// 		indicator: [
	// 			{ name: '示功仪', },
	// 			{ name: '温压一体仪' },
	// 			{ name: '压力变送器' },
	// 			{ name: '温度变送器' },
	// 			{ name: '智能电参' },
	// 			// { name: 'Marketing', max: 25000 }
	// 		],
	// 		center: ['50%', '45%'],
	// 		radius: 75,
	// 		axisName: {
	// 			formatter: '【{value}】',
	// 			color: '#ccc'
	// 		},
	// 		axisLine: {
	// 			lineStyle: {
	// 				color: 'rgba(211, 253, 250, 0.8)'
	// 			}
	// 		},
	// 		splitLine: {
	// 			lineStyle: {
	// 				color: 'rgba(211, 253, 250, 0.8)'
	// 			}
	// 		}
	// 	},
	// 	series: [
	// 		{
	// 			name: '设备运行统计',
	// 			type: 'radar',
	// 			data: [
	// 				{
	// 					value: [1001, 952, 1314, 292, 1029],
	// 					name: '正常数',
	// 					itemStyle: {
	// 						color: '#00bdff' // 设置点的颜色
	// 					},
	// 					lineStyle: {
	// 						color: '#00bdff', // 设置线的颜色
	// 						width: 2 // 设置线宽
	// 					},
	// 					// areaStyle: {
	// 					// 	color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
	// 					// 		{
	// 					// 			color: 'rgba(255, 145, 124, 0.1)',
	// 					// 			offset: 0
	// 					// 		},
	// 					// 		{
	// 					// 			color: 'rgba(255, 145, 124, 0.9)',
	// 					// 			offset: 1
	// 					// 		}
	// 					// 	])
	// 					// }
	// 				},
	// 				{
	// 					value: [60, 60, 114, 15, 69],
	// 					name: '故障数',
	// 					itemStyle: {
	// 						color: 'rgba(255, 145, 124, 1)' // 设置点的颜色
	// 					},
	// 					lineStyle: {
	// 						color: 'blue', // 设置线的颜色
	// 						width: 2 // 设置线宽
	// 					},
	// 					areaStyle: {
	// 						color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
	// 							{
	// 								color: 'rgba(255, 145, 124, 0.1)',
	// 								offset: 0
	// 							},
	// 							{
	// 								color: 'rgba(255, 145, 124, 0.9)',
	// 								offset: 1
	// 							}
	// 						])
	// 					}
	// 				}
	// 			]
	// 		}
	// 	]
	// };
	myChart5.setOption(option);
	state.myCharts.push(myChart5);
};
// 更新设备运行统计数据 右2
const updataChartsRightTwo = () => {
	myChart5.setOption({
		yAxis: [
			{
				data: chartRightTwoDatas.value.namesList,
			},
		],
		series: [
			{
				data: chartRightTwoDatas.value.deviceNormalCountsList,
			},
			{
				data: chartRightTwoDatas.value.deviceFaultsCountsList,
			},
		]
	});
}
// 获取设备运行统计数据 右2
const getrunStatisticsList = async (data?: any) => {
	if (data) {
		selectvalue.value.groupId = data.groupId
		selectvalue.value.groupName = data.groupName
	}
	const response = await runStatistics(selectvalue.value)
	const namesList = [] as any
	const deviceFaultsCountsList = [] as any
	const deviceNormalCountsList = [] as any
	response.data.data.forEach((item: any) => {
		namesList.push(item.productName)
		deviceFaultsCountsList.push(item.deviceFaultsCount)
		deviceNormalCountsList.push(item.deviceNormalCount)
	})
	chartRightTwoDatas.value.namesList = namesList
	chartRightTwoDatas.value.deviceFaultsCountsList = deviceFaultsCountsList
	chartRightTwoDatas.value.deviceNormalCountsList = deviceNormalCountsList
	console.log(chartRightTwoDatas.value, '设备运行统计');

	updataChartsRightTwo()
}
// 获取查询所属单位列表 右2
const getAffiliationList = async () => {
	try {
		const response = await Affiliation()
		if (response.data.code == 200) {
			options.value = handleTree(response.data.data, "groupId") as any
			selectvalue.value.groupId = response.data.data[0].groupId
			selectvalue.value.groupName = response.data.data[0].groupName
			console.log(options.value, '所属单位列表');
			getrunStatisticsList()
			timeout1.value = setInterval(() => {
				getrunStatisticsList()
			}, 60000);
		}

	} catch (error) {
		console.error('Error fetching table data:', error);
	} finally { }

}


// 初始化设备运维考核数据 右3
const initChartsRightThree = () => {
	const handleResize = () => {
		if (myChart6) {
			myChart6.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	const resizeObserver = new ResizeObserver(handleResize);
	resizeObserver.observe(chartsRightThreeRef.value);
	myChart6 = echarts.init(chartsRightThreeRef.value);
	const option = {
		grid: {
			top: '28%',
			right: '5%',
			bottom: '15%',
			left: '10%',
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		legend: {
			data: ['设备故障数', '故障处置数'],
			// 隐藏图例边框
			top: '5%',
			left: '3%',
			// itemWidth: 15,
			// itemHeight: 14,
			itemStyle: {
				// 去掉图例项的边框
				borderWidth: 0,  // 图例项没有边框
				borderColor: 'transparent'  // 图例项边框颜色为透明
			},
			textStyle: {
				color: '#f7f7f7',
			}

		},
		xAxis: {
			type: 'category',
			data: chartRightThreeDatas.value.DatesList,
			axisTick: {
				show: false, // (刻度线)
			},
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},
			axisLine: {
				show: true,
				lineStyle: {
					color: '#415872',
					type: 'dashed',
				}
			},
		},
		yAxis: {
			type: 'value',
			minInterval: 1,
			splitLine: {
				lineStyle: {
					type: 'dashed',
					color: '#2f4866',
				},
			},
			axisTick: {
				show: false,
			},
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},

		},
		series: [
			{
				name: '设备故障数',
				data: chartRightThreeDatas.value.deviceFaultsCountsList,
				type: 'bar',
				barWidth: 15,
				label: {
					show: true, // Show data labels
					position: 'top', // Position at the top of the bars
					color: '#2574d6', // Text color
					fontWeight: 'bold', // Font weight
				},
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2574d6' },   // 结束颜色
						{ offset: 0, color: '#0b2f50' }// 起始颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2574d6', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '故障处置数',
				data: chartRightThreeDatas.value.handledFaultsCountsList,
				type: 'bar',
				barWidth: 15,
				label: {
					show: true, // Show data labels
					position: 'top', // Position at the top of the bars
					color: '#d4c26a', // Text color
					fontWeight: 'bold', // Font weight
				},
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#dbbd3f' },   // 起始颜色
						{ offset: 0, color: '#364d4c' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#dbbd3f', // 边框固定颜色
					borderWidth: 2,
				}

			},
		],
	};
	// const option = {
	// 	tooltip: {
	// 		trigger: 'axis',
	// 		axisPointer: {
	// 			type: 'shadow'
	// 		}
	// 	},
	// 	legend: {
	// 		left: '3%',
	// 		right: '4%',
	// 		itemWidth: 15,
	// 		itemHeight: 14,
	// 		textStyle: {
	// 			color: '#f7f7f7',
	// 		}
	// 	},
	// 	grid: {
	// 		top: '15%',
	// 		left: '3%',
	// 		right: '5%',
	// 		bottom: '20%',
	// 		containLabel: true
	// 	},
	// 	xAxis: {
	// 		type: 'value',
	// 		min: 0,
	// 		max: 1500,
	// 		nameTextStyle: {
	// 			color: '#ccc',
	// 		},
	// 		axisLabel: {
	// 			formatter: '{value}',
	// 			color: '#ccc',
	// 		},
	// 		splitLine: {
	// 			show: false,
	// 		}
	// 	},
	// 	yAxis: {
	// 		type: 'category',
	// 		data: ['1月', '2月', '3月', '4月'],
	// 		axisLabel: {
	// 			formatter: '{value}',
	// 			color: '#ccc',
	// 		},
	// 		axisTick: {
	// 			show: false, // (刻度线)
	// 		},
	// 		splitLine: {
	// 			show: true,
	// 			lineStyle: {
	// 				type: 'dashed',  // 设置网格线为虚线
	// 				color: '#2f4866'    // 设置网格线颜色
	// 			}
	// 		},
	// 		axisLine: {
	// 			show: false,
	// 		},

	// 	},
	// 	series: [
	// 		{
	// 			name: '设备故障数',
	// 			type: 'bar',
	// 			stack: 'total',
	// 			barWidth: 11,
	// 			label: {
	// 				show: true
	// 			},
	// 			itemStyle: {
	// 				color: '#1e90ff',
	// 			},
	// 			emphasis: {
	// 				focus: 'series'
	// 			},
	// 			data: [1001, 952, 1314, 292, 1029],
	// 			itemStyle: {
	// 				// borderRadius: [0, 10, 10, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
	// 				color: '#ef686a',
	// 				// 使用线性渐变色
	// 				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
	// 					{ offset: 0, color: '#134b67' },   // 起始颜色
	// 					{ offset: 1, color: '#1e90ff' }// 结束颜色
	// 				]),
	// 				// 设置边框颜色为固定颜色
	// 				borderColor: '#1e90ff', // 边框固定颜色
	// 				borderWidth: 2,
	// 			}
	// 		},
	// 		{
	// 			name: '故障处置数',
	// 			type: 'bar',
	// 			stack: 'total',
	// 			label: {
	// 				show: true,
	// 				color: '#ffffff',
	// 			},
	// 			// itemStyle: {
	// 			// 	color: '#ff69b4',
	// 			// },
	// 			emphasis: {
	// 				focus: 'series'
	// 			},
	// 			data: [68, 60, 114, 15, 69,],
	// 			itemStyle: {
	// 				// borderRadius: [0, 10, 10, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
	// 				color: '#ef686a',
	// 				// 使用线性渐变色
	// 				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
	// 					{ offset: 0, color: '#134b67' },   // 起始颜色
	// 					{ offset: 1, color: '#ef686a' }// 结束颜色
	// 				]),
	// 				// 设置边框颜色为固定颜色
	// 				borderColor: '#ef686a', // 边框固定颜色
	// 				borderWidth: 2
	// 			}
	// 		},

	// 	]
	// };
	myChart6.setOption(option);
	state.myCharts.push(myChart6);
};
// 更新设备运维考核数据 右3
const updataChartsRightThree = () => {
	myChart6.setOption({
		xAxis: [
			{
				data: chartRightThreeDatas.value.DatesList,
			}
		],
		series: [
			{
				data: chartRightThreeDatas.value.deviceFaultsCountsList,
			},
			{
				data: chartRightThreeDatas.value.handledFaultsCountsList,
			}
		]
	});
}
// 获取设备运维考核数据 右3
const getassessEvaluationList = async () => {
	const response = await assessEvaluation()
	const DatesList = [] as any
	const handledFaultsCountsList = [] as any
	const deviceFaultsCountsList = [] as any
	response.data.data.forEach((item: any) => {
		DatesList.push(item.date + '月')
		handledFaultsCountsList.push(item.handledFaultsCount)
		deviceFaultsCountsList.push(item.deviceFaultsCount)
	})
	chartRightThreeDatas.value.DatesList = DatesList
	chartRightThreeDatas.value.handledFaultsCountsList = handledFaultsCountsList
	chartRightThreeDatas.value.deviceFaultsCountsList = deviceFaultsCountsList
	console.log(chartRightThreeDatas.value, '设备运维考核');
	updataChartsRightThree()
}


// 初始化设备故障区域排名 右4
const initChartsRightFour = () => {
	const handleResize = () => {
		if (myChart7) {
			myChart7.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	const resizeObserver = new ResizeObserver(handleResize);
	resizeObserver.observe(chartsRightFourRef.value);
	myChart7 = echarts.init(chartsRightFourRef.value);
	const option = {
		grid: {
			top: '5%',
			bottom: '5%',
			left: '22%',
			right: '5%'
		},
		legend: {
			show: false
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		xAxis: {
			show: false,
			max: 'dataMax',
			nameTextStyle: {
				color: '#ccc',
			},
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},
			splitLine: {
				show: false,
			}

		},
		yAxis: {
			type: 'category',
			data: chartRightFourDatas.value.namesList,
			inverse: true,
			animationDuration: 300,
			animationDurationUpdate: 300,
			axisLabel: {
				align: "left",
				margin: 110, // 距离右侧图形距离，配合axisLabel.left 和 grid.left 使用
				color: '#ccc',
				// 使用 formatter 来添加索引标签
				formatter: (params: any, index: any) => {
					let result;
					if (index == 0) {
						result = `{a1|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 1) {
						result = `{a2|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 2) {
						result = `{a3|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 3) {
						result = `{a4|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 4) {
						result = `{a5|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 5) {
						result = `{a6|${index + 1}}${" ".repeat(2)}${params}`;
					} else {
						result = `{a7|${index + 1}}${" ".repeat(2)}${params}`;
					}
					return result;
				},
				// formatter: '{value}',
				rich: {
					a1: {
						color: "#fff",
						backgroundColor: "#EA2739",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a2: {
						color: "#fff",
						backgroundColor: "#FF8C40",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a3: {
						color: "#fff",
						backgroundColor: "#FFC600",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a4: {
						color: "#fff",
						backgroundColor: "#438d15",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a5: {
						color: "#fff",
						backgroundColor: "#8B5CF6",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a6: {
						color: "#fff",
						backgroundColor: "#F472B6",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a7: {
						color: "#fff",
						backgroundColor: "#3C7DF9",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
				}
			},
			axisTick: {
				show: false, // (刻度线)
			},
			// splitLine: {
			// 	show: true,
			// 	lineStyle: {
			// 		type: 'dashed',  // 设置网格线为虚线
			// 		color: '#2f4866'    // 设置网格线颜色
			// 	}
			// },
			axisLine: {
				show: false,
			},
		},
		series: [
			{
				realtimeSort: true,
				name: '故障次数',
				type: 'bar',
				data: chartRightFourDatas.value.deviceFaultsCountsList,
				barWidth: 11,
				label: {
					show: true,
					position: 'right',
					valueAnimation: true,
					color: '#ffffff',
				},
				itemStyle: {
					borderRadius: [10, 10, 10, 10],
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: '#2cb5cc' },
						{ offset: 0.5, color: '#188df0' },
						{ offset: 1, color: '#2574d6' }
					])
				},
				emphasis: {
					itemStyle: {
						color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
							{ offset: 0, color: '#2574d6' },
							{ offset: 0.5, color: '#2378f7' },
							{ offset: 1, color: '#2cb5cc' }
						])
					}
				},
			}
		],

		animationDuration: 0,
		animationDurationUpdate: 3000,
		animationEasing: 'linear',
		animationEasingUpdate: 'linear'
	};
	myChart7.setOption(option as any);
	state.myCharts.push(myChart7);
};
// 更新设备故障区域排名数据 右4
const updataChartsRightFour = () => {
	myChart7.setOption({
		yAxis: [
			{
				data: chartRightFourDatas.value.namesList,
			}
		],
		series: [
			{
				data: chartRightFourDatas.value.deviceFaultsCountsList,
			}
		]
	});
}
// 获取设备故障区域排名数据 右4
const getregionalRankingsList = async () => {
	const response = await regionalRankings()
	const namesList = [] as any
	const deviceFaultsCountsList = [] as any
	response.data.data.forEach((item: any) => {
		namesList.push(item.groupName)
		deviceFaultsCountsList.push(item.count)
	})
	chartRightFourDatas.value.namesList = namesList
	chartRightFourDatas.value.deviceFaultsCountsList = deviceFaultsCountsList
	console.log(chartRightFourDatas.value, '设备故障区域排名');
	updataChartsRightFour()
}
// 获取运维看板所有列表数据
const getAllList = () => {
	getMonitorRangList()
	getMonitorDynamicsList()
	getFaultsMalfunctionList()
	getCompletenessRate()
	getfaultsWeekList()
	getassessEvaluationList()
	getregionalRankingsList()
	getfaultsList()
	timeout.value = setInterval(() => {
		getMonitorRangList()
		getMonitorDynamicsList()
		getFaultsMalfunctionList()
		getCompletenessRate()
		getfaultsWeekList()
		getassessEvaluationList()
		getregionalRankingsList()
		getfaultsList()
	}, 60000);
}
// 初始化echarts
const allchartsinit = () => {
	// initChartsLeftOne()
	initChartsLeftTwo()
	initChartsLeftThree();
	initChartsLeftFour()
	// initChartsCenterOne();
	// initChartsCenterTwo();
	initChartsRightOne();
	initChartsRightTwo()
	initChartsRightThree();
	initChartsRightFour()
	// initEchartsResize();
	getAffiliationList()
}
// 批量设置 echarts resize
const initEchartsResizeFun = () => {
	nextTick(() => {
		for (let i = 0; i < state.myCharts.length; i++) {
			state.myCharts[i].resize();
		}
	});
};
// 批量设置 echarts resize
const initEchartsResize = () => {
	window.addEventListener('resize', initEchartsResizeFun);
};
// 页面加载时
onMounted(() => {
	allchartsinit()
	getAllList()
	nextTick(() => {
		autoScrolling();
	});

});
onBeforeUnmount(() => {
	myChart1.dispose(); // 销毁图表实例
	myChart2.dispose(); // 销毁图表实例
	myChart3.dispose(); // 销毁图表实例
	myChart4.dispose(); // 销毁图表实例
	myChart5.dispose(); // 销毁图表实例
	myChart6.dispose(); // 销毁图表实例
	myChart7.dispose(); // 销毁图表实例
	clearInterval(timeout.value);    // 清除单次定时器
	clearInterval(timeout1.value);   // 清除单次定时器
	clearInterval(intervalId.value); // 清除循环定时器
});
//鼠标进入，停止滚动
const onMouseenter = () => {
	isAutoScrolling.value = false;
};
//鼠标移出，继续滚动
const onMouseleave = () => {
	isAutoScrolling.value = true;
};
// 由于页面缓存原因，keep-alive
onActivated(() => {
	initEchartsResizeFun();
});
// 监听 pinia 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
watch(
	() => isTagsViewCurrenFull.value,
	() => {
		initEchartsResizeFun();
	}
);
</script>

<style scoped lang="scss">
@import './chart.scss';

@font-face {
	font-family: electronicFont;
	src: url('../../../public/font/DS-DIGIT.TTF') format('truetype');
	font-display: swap;
	/* 使用swap来快速显示文字 */
}

:deep(*) {
	cursor: url(./images/pointer.png) 8 3, auto !important;
}

.top-RightOne {
	color: #ffffff;
	display: flex;
	width: 100%;
	justify-content: space-between;
	align-items: center;

	.leftcss {
		width: 45%;
		height: 30px;
		display: flex;
		align-items: center;
		border-radius: 20px 0 0 20px;
		background: linear-gradient(to right, #0f55e1, rgba(0, 0, 0, 0));

		.title {
			margin: 0 20px 0 20px;
			font-size: 15px;

		}

		.num {
			background-image: -webkit-linear-gradient(135deg,
					#6ac7ff,
					#bce6fe 25%,
					#97d4f8 50%,
					#74cbff 75%,
					#4b9fdc);
			-webkit-text-fill-color: transparent;
			-webkit-background-clip: text;
		}
	}

	.rightcss {
		width: 45%;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		background: linear-gradient(to left, #097f6f, rgba(0, 0, 0, 0));
		border-radius: 0 20px 20px 0;

		.title {
			margin: 0 20px 0 20px;
			font-size: 15px;
		}

		.num {
			background-image: -webkit-linear-gradient(135deg,
					#4db5ae,
					#44b7aa 25%,
					#41c8b2 50%,
					#2cb99f 75%,
					#21b495);
			-webkit-text-fill-color: transparent;
			-webkit-background-clip: text;
		}
	}
}

.instrumentList {
	width: 100%;
	height: 100%;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
	align-content: center;

	.clearfix {
		width: 20%;
		height: 45%;
	}

	.li {
		// float: left;
		width: 100%;
		height: 100%;
		padding: 0 0 5PX 0;
		display: flex;
		flex-direction: column;
		justify-content: center;

		span {
			opacity: .6;
			font-size: 12px;
			width: 100%;
			text-align: center;
		}
	}

	.yq {
		width: 2.7vw;
		height: 2.7vw;
		margin: 0 auto 5px auto;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 25px;
		font-family: electronicFont;
		color: #fff32b;
	}

	.yq:before {
		position: absolute;
		width: 100%;
		height: 100%;
		content: "";
		background: url('./images/img1.png') center center;
		border-radius: 100px;
		background-size: 100% 100%;
		opacity: .3;
		left: 0;
		top: 0;
		animation: myfirst2 30s infinite linear;
	}

	.yq:after {
		position: absolute;
		width: 86%;
		background: url('./images/img2.png') center center;
		border-radius: 100px;
		background-size: 100% 100%;
		opacity: .3;
		height: 86%;
		content: "";
		// left: 7%;
		// top: 7%;
		animation: myfirst 30s infinite linear;
	}

	@keyframes myfirst {
		to {
			transform: rotate(-360deg)
		}
	}

	@keyframes myfirst2 {
		to {
			transform: rotate(360deg)
		}
	}
}


/* 给特定的el-select的选项设置背景颜色 */
:deep(.my-select .el-select__wrapper) {
	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	box-shadow: none;
	/* 设置选项背景色 */
	// { offset: 0, color: '#134b67' },   // 起始颜色
	// 					{ offset: 1, color: '#1e90ff' }/
}

:deep(.my-select span) {
	// background-color: red !important;
	box-shadow: none;
	color: #ffffff !important;
	/* 设置选项背景色 */
}

:deep(.my-select svg) {
	// background-color: red !important;
	box-shadow: none;
	color: #ffffff !important;
	/* 设置选项背景色 */
}

:deep(.el-popper) {
	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// border: none;
}
</style>
<style lang="scss">
.mySelectStyle {

	// 下拉框移入hover
	// .el-tree-node__children {
	// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// }


	// .el-popper__arrow:before {
	// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// 	border: 1px solid linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// }

	//下拉边框颜色
	// .el-select-dropdown__list {
	// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// 	// padding: !important;
	// }

	// 小方块
	// .el-popper[data-popper-placement^=bottom] .el-popper__arrow::before  {
	// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// 	border: 1px solid linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// }


	.el-tree-node {
		background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	}

	// 父级背景图 字体颜色
	.el-tree-node__content {
		background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	}

	// 字体颜色
	.el-select-dropdown__item span {
		color: #ffffff !important;
	}

	.el-tree-node__content:hover {
		background: rgba(255, 255, 255, 0.1) !important;
	}
}

// //下拉框背景色
.mySelectStyle.el-popper.is-light.mySelectStyle {
	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	border: 1px solid #004f73 !important;
}

.mySelectStyle.el-popper[data-popper-placement^=bottom] .el-popper__arrow::before {
	background: #006c93 !important;
	border: none;
}

// .mySelectStyle.el-select-dropdown__item {
// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// }

// //下拉框的链接小方块
// .mySelectStyle.el-select-dropdown__item.is-active {
// 	background: #254277 !important;
// }

// //鼠标移动上去的选中色
// .mySelectStyle.el-select-dropdown__item.hover {
// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// 	border: 1px solid #004f73 !important;
// }

// //下拉框背景色
// .mySelectStyle.el-popper.is-light.mySelectStyle {
// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// 	border: 1px solid #004f73 !important;
// }


// //下拉框的链接小方块
// .mySelectStyle.el-popper[data-popper-placement^=bottom] .el-popper__arrow::before {
// 	background: #006c93 !important;
// 	border: none;
// }

// .mySelectStyle.el-popper[data-popper-placement^=top] .el-popper__arrow::before {
// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// 	border: 1px solid linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// }

// //鼠标移动上去的选中色
// .mySelectStyle {
// 	padding: 0 !important;

// 	/* 处理鼠标移出后的背景颜色 */
// 	.el-select-dropdown .el-select-dropdown__item {
// 		background: transparent !important;
// 		/* 或你想要的背景色 */
// 	}

// 	.el-select-dropdown__item.hover,
// 	.el-select-dropdown__item:hover {
// 		background: rgba(255, 255, 255, 0.1) !important;
// 	}

// 	/* 处理选中项的背景颜色 */
// 	.el-select-dropdown .el-select-dropdown__item.selected {
// 		background: rgba(255, 255, 255, 0.1) !important;
// 	}



// 	//下拉框的文本颜色
// 	.el-select-dropdown__item {
// 		color: #ffffff !important;
// 		font-size: 12px
// 	}
// }

::-webkit-scrollbar {
	display: none;
	// background-color: none;
}
</style>
