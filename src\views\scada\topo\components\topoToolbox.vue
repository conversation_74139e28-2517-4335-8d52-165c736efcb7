<template>
    <el-menu class="topo-toolbox" unique-opened @open="handleOpen">
        <el-sub-menu class="sub-menu" index="base">
            <template #title>
                <div class="sub-menu-title">
                    <SvgIcon name="base-cmpt" :type="'menu'" :color="'#000000'" />
                    <span class="name-wrap">基本组件</span>
                </div>
            </template>
            <data-panel class="panel-wrap" ref="dataPanel" :data="jsonBase" />
        </el-sub-menu>
        <el-sub-menu class="sub-menu" index="shape">
            <template #title>
                <div class="sub-menu-title">
                    <SvgIcon name="line-shape" :type="'menu'" :color="'#000000'" />
                    <span class="name-wrap">基本形状</span>
                </div>
            </template>
            <data-panel class="panel-wrap" ref="dataPanel" :data="jsonShape" />
        </el-sub-menu>
        <el-sub-menu class="sub-menu" index="chart">
            <template #title>
                <div class="sub-menu-title">
                    <SvgIcon name="chart" :type="'menu'" :color="'#000000'" />
                    <span class="name-wrap">统计图形</span>
                </div>
            </template>
            <data-panel class="panel-wrap" ref="dataPanel" :data="jsonChart" />
        </el-sub-menu>
        <!-- <el-sub-menu index="Icon图库">
            <template slot="title">
                <div class="sub-menu-title">
                    <svg-icon class="icon-wrap" icon-class="icon" />
                    <span class="name-wrap">Icon图库</span>
                </div>
            </template>
            <DataPanel ref="dataPanel" :data="jsonIcon" />
        </el-sub-menu> -->
        <el-sub-menu class="sub-menu" index="gallery">
            <template #title>
                <div class="sub-menu-title">
                    <SvgIcon name="picture" :type="'menu'" :color="'#000000'" />
                    <span class="name-wrap">图库组件</span>
                </div>
            </template>
            <el-sub-menu class="sub-menu" v-for="dict in gallery_type_list" :index="dict.dictValue"
                :key="dict.dictValue">
                <template #title>
                    <span style="color: #000000;">{{ dict.dictLabel }}</span>
                </template>
                <data-panel v-loading="loading" element-loading-background="transparent" class="panel-wrap"
                    ref="dataPanel" :data="gallerys" />
            </el-sub-menu>
        </el-sub-menu>
        <el-sub-menu class="sub-menu" index="echart">
            <template #title>
                <div class="sub-menu-title">
                    <SvgIcon name="redis" :type="'menu'" :color="'#000000'" />
                    <span class="name-wrap">图表组件</span>
                </div>
            </template>
            <el-sub-menu class="sub-menu" v-for="dict in echart_type_list" :index="dict.dictValue"
                :key="dict.dictValue">
                <template #title>
                    <span style="color: #000000;">{{ dict.dictLabel }}</span>
                </template>
                <data-panel v-loading="loading" element-loading-background="transparent" class="panel-wrap"
                    ref="dataPanel" :data="echarts" />
            </el-sub-menu>
        </el-sub-menu>
        <el-sub-menu class="sub-menu" index="more">
            <template #title>
                <div class="sub-menu-title">
                    <!-- <svg-icon class="icon-wrap" icon-class="product" /> -->
                    <SvgIcon name="product" :type="'menu'" :color="'#000000'" />
                    <span class="name-wrap">更多组件</span>
                </div>
            </template>
            <data-panel v-loading="loading" element-loading-background="transparent" class="panel-wrap" ref="dataPanel"
                :data="components" />
        </el-sub-menu>
    </el-menu>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DataPanel from './data-panel/index.vue';
import jsonIcon from './data-toolbox/icon.json';
import jsonBase from './data-toolbox/base.json';
import jsonShape from './data-toolbox/shape.json';
import jsonChart from './data-toolbox/chart.json';
import { listGallery } from '/@/api/scada/gallery';
import { listEchart } from '/@/api/scada/echart';
import { listComponent } from '/@/api/scada/component';
// const dataPanel  = ref()
const dictStore = useDictStore();  // 使用 Pinia store
const baseApi = ref(import.meta.env.VITE_APP_BASE_API)
// jsonBase: jsonBase, // 基本组件
// jsonShape: jsonShape,
// jsonChart: jsonChart,
// jsonIcon: jsonIcon,
const loading = ref(false)
const gallerys = ref({}) // 图库数据
const echarts = ref({}) // 图表数据
const components = ref({}) // 组件数据
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const gallery_type_list = ref<TypeOption[]>([])
const echart_type_list = ref<TypeOption[]>([])
const handleOpen = (key: any, keyPath: any[]) => {
    const type = keyPath[0];
    if (type == 'gallery' && key !== type) {
        getGalleryDatas(type, key);
    } else if (type == 'echart' && key !== type) {
        getEchartDatas(type, key);
    } else {
        getComponent('component');
    }
}
// 获取图库数据
const getGalleryDatas = (type: any, name: any) => {
    loading.value = true;
    const params = {
        pageNum: 1,
        pageSize: 10,
        categoryName: name,
        moduleGuid: '云组态',
        orderByColumn: 'id',
        isAsc: 'desc',
    };
    listGallery(params).then((res) => {
        if (res.data.code === 200) {
            if (res.data.rows.length > 0) {
                let newJson = getJson(type);
                res.data.rows.forEach((item: { categoryName: any; fileName: any; resourceUrl: any; }) => {
                    newJson.title = item.categoryName;
                    let newJsonItem = getImgJsonItem();
                    newJsonItem.text = item.fileName;
                    newJsonItem.icon = baseApi.value + item.resourceUrl;
                    newJsonItem.info.style.url = baseApi.value + item.resourceUrl;
                    newJson.items.push(newJsonItem);
                });
                gallerys.value = newJson;
            } else {
                gallerys.value = {};
            }
        }
        loading.value = false;
    });
}
// 获取图表数据
const getEchartDatas = (type: any, name: any) => {
    loading.value = true;
    const params = {
        pageNum: 1,
        pageSize: 9999,
        echartType: name,
    };
    listEchart(params).then((res) => {
        if (res.data.code === 200) {
            if (res.data.rows.length > 0) {
                let newJson = getJson(type);
                res.data.rows.forEach((item: { categoryName: any; echartName: any; echartImgae: any; id: any; }) => {
                    newJson.title = item.categoryName;
                    let newJsonItem = getEchartJsonItem() as any;
                    newJsonItem.text = item.echartName;
                    newJsonItem.icon = baseApi.value + item.echartImgae;
                    newJsonItem.info.dataBind.echartOption = `echartId-${item.id}`;
                    newJson.items.push(newJsonItem);
                });
                console.log(newJson,'getEchartDatas');
                echarts.value = newJson;
            } else {
                echarts.value = {};
            }
        }
        loading.value = false;
    });
}
// 组件数据
const getComponent = (type: any) => {
    loading.value = true;
    const params = {
        pageNum: 1,
        pageSize: 9999,
    };
    listComponent(params).then((res) => {
        if (res.data.code === 200) {
            if (res.data.rows.length > 0) {
                let newJson = getJson(type);
                res.data.rows.forEach((item: { categoryName: any; componentName: any; componentImage: any; id: any; }) => {
                    newJson.title = item.categoryName;
                    let newJsonItem = getComponentJsonItem();
                    newJsonItem.text = item.componentName;
                    newJsonItem.icon = baseApi.value + item.componentImage;
                    newJsonItem.info.dataBind.componentId = item.id;
                    newJson.items.push(newJsonItem);
                });
                console.log(newJson,'getComponent');
                
                components.value = newJson;
            } else {
                components.value = {};
            }
        }
        loading.value = false;
    });
}
const getJson = (type: any) => {
    let newJson = {
        title: '',
        icon: type,
        opened: false,
        items: [],
    };
    return newJson;
}
// 生成图片json
const getImgJsonItem = () => {
    let newJson = {
        text: '',
        icon: 'image',
        type: 'service',
        info: {
            type: 'image',
            componentShow: ['动画', '单击', '组件颜色', '滤镜渲染', '组件填充', '参数绑定'],
            action: [],
            hdClassName: '',
            dataBind: {
                action: '',
                productId: '',
                serialNumber: '',
                identifier: '',
                modelName: '',
                modelValue: '',
                redirectUrl: '',
                stateList: [],
            },
            dataAction: {
                serialNumber: '',
                identifier: '',
                modelName: '',
                paramJudge: '',
                paramJudgeData: '',
                rotationSpeed: '中',
                translateList: [],
            },
            style: {
                position: {
                    x: 200,
                    y: 200,
                    w: 100,
                    h: 100,
                },
                backColor: 'rgba(255, 255, 255, 0)',
                foreColor: '',
                zIndex: 1,
                transform: 0,
                url: '',
                transformType: 'rotate(0deg)',
                isFilter: true,
            },
        },
    };
    return newJson;
}
// 生成图表json
const getEchartJsonItem = () => {
    let newJson = {
        text: '',
        icon: 'custom',
        type: 'service',
        info: {
            type: 'chart-wrapper',
            componentShow: ['自定义echarts'],
            action: [],
            dataBind: {
                echartOption: '',
                echartRun: 0,
                echartSecond: 60,
                echartData: '',
            },
            style: {
                position: {
                    x: 0,
                    y: 0,
                    w: 350,
                    h: 250,
                },
                backColor: 'rgba(255, 255, 255, 1)',
                foreColor: '#000',
                zIndex: 1,
                transform: 0,
                transformType: 'rotate(0deg)',
            },
        },
    };
    return newJson;
}
// 生成组件json
const getComponentJsonItem = () => {
    let newJson = {
        text: '',
        icon: 'custom',
        type: 'service',
        info: {
            type: 'component',
            componentShow: ['动画'],
            action: [],
            dataBind: {},
            dataAction: {
                xyAction: false,
                xzAction: false,
                ssAction: false,
                hdAction: false,
                serialNumber: '',
                identifier: '',
                modelName: '',
                paramJudge: '',
                paramJudgeData: '',
                rotationSpeed: '中',
                translateList: [],
            },
            style: {
                position: {
                    x: 0,
                    y: 0,
                    w: 350,
                    h: 250,
                },
                backColor: 'rgba(255, 255, 255, 1)',
                foreColor: '#000',
                zIndex: 1,
                transform: 0,
                transformType: 'rotate(0deg)',
            },
        },
    };
    return newJson;
}
const onDragstart = (event: { dataTransfer: { setData: (arg0: string, arg1: string) => void; }; }, info: { info: any; }) => {
    var infoJson = JSON.stringify(info.info);
    event.dataTransfer.setData('my-info', infoJson);
}
// 获取状态数据
const getdictdata = async () => {
    try {
        gallery_type_list.value = await dictStore.fetchDict('scada_gallery_type')
        echart_type_list.value = await dictStore.fetchDict('scada_echart_type')
        // 处理公告数据
    } catch (error) {
        console.error('获取公告数据失败:', error);
    }
};
// 页面加载时
onMounted(() => {
    getdictdata()
});
</script>

<style lang="scss" scoped>
.topo-toolbox {
    background-color: #f1f3f4;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;

    .el-sub-menu {
        background-color: #f1f3f4;

        .sub-menu-title {
            display: flex;
            flex-direction: row;
            align-items: center;
            font-size: 14px;

            .icon-wrap {
                width: 16px;
                height: 16px;
            }

            .name-wrap {
                margin-left: 8px;
                color: #000000;
            }
        }

        .panel-wrap {
            padding: 5px;
        }
    }
}
</style>
